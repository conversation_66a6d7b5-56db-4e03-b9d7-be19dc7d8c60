export const BaseURL = '/bpms-api/';

export const WAREHOUSING_RULE_CONFIGUE_SERVICE = '/warehousingRuleConfigue';

const ApiUrl = {
  //  数据重播
  DataReplay: {
    getWaferLotDropdown: '/dataReplay/filterWarehousingLotWaferDropDown', //waferLot下拉列表
    getFileDropdown: '/dataReplay/filterWarehousingFileDropDown', //file下拉列表
    getDrDropdown: '/dataReplay/filterReplayRecordDropDown', //重播下拉列表
    getWaferLotList: '/dataReplay/queryWarehousingLotWafer', //waferLot列表
    getFileList: '/dataReplay/queryWarehousingFile', //file列表
    getDrList: '/dataReplay/queryReplayRecord', //重播列表
    replay: '/dataReplay/replayByFileOperate', //重播
    retryReplay: '/dataReplay/retryReplay', //重试
    cancelReplay: '/dataReplay/cancelReplay', //取消
    topReplay: '/dataReplay/topReplay', //置顶
    fileReplay: '/dataReplay/replayByFileOperate', //文件重播
    lotWaferReplay: '/dataReplay/replayByLotWaferOperate', //lotWafer重播

    getDataEntryList: '/dataReplay/queryWarehousingRecord', //数据入库列表
    getDataReplayList: '/dataReplay/queryReplayRecord', //数据重播列表
    singleReplay: '/dataReplay/singleReplay', //单行重播
    batchReplay: '/dataReplay/batchReplay', //批量重播
    getTaskDropdown: '/dataReplay/filterReplayTaskDropDown', //重试
    getTaskList: '/dataReplay/queryReplayTaskPage', //查看
    getFileProcessDetail: '/dataReplay/getFileProcessDetail', //获取文件流程详情
    getFactoryDropdown: '/dataReplay/getFactoryDropDown', //获取Factory下拉框
    validateFileNameList: '/dataReplay/validateFileNameList', //验证文件名列表的Test Area和File Category
  },

  //  Task
  Task: {
    getDwDropdown: '/task/dwTaskDropDownFilter', //DW下拉列表
    getYmsDropdown: '/task/ymsTaskDropDownFilter', //YMS下拉列表
    rerunDw: '/task/rerunDwTask', //DW重跑
    rerunYms: '/task/rerunYmsTask', //YMS重跑
    rerunDmt: '/task/rerunDmtSingleTask', //Dmt重跑
    freezeDmt: '/task/reloadDwTask', //Dmt冻结
    getDetail: '/task/querySingDwTaskDetail', //获取详情
    getDwList: '/task/queryDwTasks', //DW列表
    getYmsList: '/task/queryYmsTasks', //YMS列表
    getYmsDetail: '/task/querySingleDmtAppDetail', //YMS详情
  },

  //  数据质量
  DataQuality: {
    getList: '/dataQuality/queryRules', //列表
    importRule: '/dataQuality/importRule', //导入
    exportRule: '/dataQuality/exportRule', //导出
    saveRule: '/dataQuality/saveRule', //保存
    tryRun: '/dataQuality/tryRun', //试运行
    switchRuleStatus: '/dataQuality/switchRuleStatus', //状态切换
    queryRuleResult: '/dataQuality/queryRuleResult', //规则结果
    queryDetail: '/dataQuality/queryDetail', //规则详情
    deleteRule: '/dataQuality/deleteRule', //规则删除
  },

  //  元数据
  MetaData: {
    getLinks: '/metadata/getLinks', //链接列表
    addLink: '/metadata/addLink', //添加链接
    deleteLink: '/metadata/deleteLink', //删除链接
    getSchema: '/metadata/getSchema', //分页查询字段
    updateField: '/metadata/updateField', //修改字段
    reloadSchema: '/metadata/reloadSchema', //从表重新解析字段
    deleteField: '/metadata/deleteField', //删除字段
    getDescription: '/metadata/getDescription', //查询描述
    updateDescription: '/metadata/updateDescription', //修改描述
    getTableLineage: '/metadata/getTableLineage', //获取表级血缘
    getFieldLineage: '/metadata/getFieldLineage', //获取字段血缘
    getFieldInfo: '/metadata/getFieldInfo', //血缘字段信息
    getDatabases: '/metadata/getDatabases', //根据数据源查库
    getTableNames: '/metadata/getTableNames', //根据数据源和库查表
  },

  //  数据清理
  ClearRulePreview: {
    getDropdown: '/clearRulePreview/filterDropDown', //下拉框
    getList: '/clearRulePreview/queryRecord', //获取列表
    retry: '/clearRulePreview/retry', //重试
    upload: '/clearRulePreview/upload', //上传
    downloadDetail: '/clearRulePreview/downloadDetail', //下载
  },

  //  业务计算管理
  ServiceManagement: {
    getList: '/computeManage/list', //列表
    updateConfig: '/computeManage/updateConfig', //更新配置
    addConfig: '/computeManage/addConfig', //添加配置
    deleteConfig: '/computeManage/deleteConfig', //删除配置
  },

  //
  Priority: {
    getAutoList: '/priority/autoTasks',
    getSparkList: '/priority/sparkTasks',
    getManualList: '/priority/manualUploadTasks',
    updatePriority: '/priority/updatePriority',
    getConfigList: '/priority/getConfigs',
    addConfig: '/priority/addConfig',
    deleteConfig: '/priority/deleteConfig',
    updateConfig: '/priority/updateConfig',
  },

  // DC迁移过来的的数据质量
  DataCenterQuality: {
    getConstantMap: '/dataQualityOverview/getConstantMap',
    getQualityOverviewOptions: '/dataQualityOverview/filter',
    getQualityOverviewData: '/dataQualityOverview/overview',

    getTimelinessOptions: '/dataTimeliness/filter',
    getTimeliness: '/dataTimeliness/dbtime',

    getIntegrityOptions: '/dataIntegrity/filter',
    getDataQualityIntegrityBase: '/dataIntegrity/integrityBase',
    getDataQualityIntegrityList: '/dataIntegrity/dataIntegrity',
    deleteDataQualityIntegrityList: '/dataIntegrity/delete',
    rerunDataQualityIntegrityList: '/dataIntegrity/reRun',
    remarkDataQualityIntegrityList: '/dataIntegrity/remark',
    changeFileLabelDataQualityIntegrityList: '/dataIntegrity/fileLabel',
    repairDataQualityIntegrityList: '/dataIntegrity/repair',
    downLoadDataExactStdfFile: '/dataIntegrity/download',
    getIntegrityExport: '/dataIntegrity/export',

    getExactOptions: '/dataAccuracy/filter',
    getExactMatchDetail: '/dataAccuracy/accuracyBase',
    getExactYieldDetail: '/dataAccuracy/accuracy',
    downloadGoodRetest: '/dataAccuracy/downloadGoodRetest',
    downloadFileRetest: '/dataAccuracy/downloadOnlineRetest',
    getExactExport: '/dataAccuracy/export',
    getExactDetailList: '/dataAccuracy/detail',

    getBatchChangeOptions: '/dataRepairRecord/batchRecord/getBatchRecordDropdown',
    getFileRecoveryOptions: '/dataRepairRecord/fileRecord/getFileRecordDropdown',
    getFileDeleteOptions: '/dataIntegrity/delete/dropDown',
    getBatchList: '/dataRepairRecord/batchRecord/getBatchRecordList',
    getRecoveryList: '/dataRepairRecord/fileRecord/getFileRecordList',
    getFileDialogList: '/dataRepairRecord/batchRecord/getFileList',
    getDeleteList: '/dataIntegrity/delete/list',

    getDialogTableData: '/dataRepairRecord/fileRecord/getFileLogList',
    getStepOneData: '/dataAccuracy/getRepairFileRecord',
    getStepTwoData: '/dataAccuracy/getRepairFieldInfo',
    getStepThreeData: '/dataAccuracy/getRepairEffectBatchFileList',
    getStepFourData: 'dataAccuracy/repairPreview',
    stepSave: 'dataAccuracy/triggerRepair',
  },
  ConfMgt: {
    getConstantMap: WAREHOUSING_RULE_CONFIGUE_SERVICE + '/getConstantMap',
    getFilterDropDown: WAREHOUSING_RULE_CONFIGUE_SERVICE + '/filterWarehousingRuleDropDown',
    getAutoImportRules: WAREHOUSING_RULE_CONFIGUE_SERVICE + '/getWarehousingRuleFlow',
    deleteWarehousingRuleFlow: WAREHOUSING_RULE_CONFIGUE_SERVICE + '/deleteWarehousingRuleFlow',
    duplicateWarehousingRuleFlow: WAREHOUSING_RULE_CONFIGUE_SERVICE + '/duplicateWarehousingRuleFlow',
    checkAutoImportRuleExist: WAREHOUSING_RULE_CONFIGUE_SERVICE + '/checkConfExists',
    validateRuleFlow: WAREHOUSING_RULE_CONFIGUE_SERVICE + '/validateWarehousingRuleFlow',
    getSubCustomerRule: WAREHOUSING_RULE_CONFIGUE_SERVICE + '/getDwSftpSubCustomerRule',
    getFileRenameRule: WAREHOUSING_RULE_CONFIGUE_SERVICE + '/getDwSftpFileRenameRule',
    getSpecialLogic: WAREHOUSING_RULE_CONFIGUE_SERVICE + '/getDwDataClearRule',
    getConvertScript: WAREHOUSING_RULE_CONFIGUE_SERVICE + '/getDwSftpFileConvertScript',
    getCriticalField: WAREHOUSING_RULE_CONFIGUE_SERVICE + '/getDwSourceStandardFieldRule',
    getCriticalFieldDeviceId: WAREHOUSING_RULE_CONFIGUE_SERVICE + '/getDwSourceStandardDeviceIdRule',
    checkConfSetting: WAREHOUSING_RULE_CONFIGUE_SERVICE + '/checkConfExists',
    getConvertScriptFileOptions: WAREHOUSING_RULE_CONFIGUE_SERVICE + '/extractScriptList',
    downloadScriptFile: WAREHOUSING_RULE_CONFIGUE_SERVICE + '/downloadScriptFileById',

    saveRuleFlow: WAREHOUSING_RULE_CONFIGUE_SERVICE + '/saveWarehousingRuleFlow',
    saveSubCustomerRule: WAREHOUSING_RULE_CONFIGUE_SERVICE + '/saveDwSftpSubCustomerRule',
    saveFileRenameRule: WAREHOUSING_RULE_CONFIGUE_SERVICE + '/saveDwSftpFileRenameRule',
    saveSpecialLogic: WAREHOUSING_RULE_CONFIGUE_SERVICE + '/saveDwDataClearRule',
    saveConvertScript: WAREHOUSING_RULE_CONFIGUE_SERVICE + '/saveDwSftpFileConvertScript',
    saveCriticalField: WAREHOUSING_RULE_CONFIGUE_SERVICE + '/saveDwSourceStandardFieldRule',
    saveCriticalFieldDeviceId: WAREHOUSING_RULE_CONFIGUE_SERVICE + '/saveDwSourceStandardDeviceIdRule',

    importRule: WAREHOUSING_RULE_CONFIGUE_SERVICE + '/importWarehousingRule',
    getExportRuleMessage: WAREHOUSING_RULE_CONFIGUE_SERVICE + '/exportWarehousingRuleMessage',
    exportRule: WAREHOUSING_RULE_CONFIGUE_SERVICE + '/exportWarehousingRule',

    //
  },
  BinDefinition: {
    searchTestPrograms: '/binDefinition/search/testPrograms',
    configured: '/binDefinition/configured',
    unconfigured: '/binDefinition/unconfigured',
    details: '/binDefinition/details',
    save: '/binDefinition/save',
    delete: '/binDefinition/delete',
    export: '/binDefinition/export',
    import: '/binDefinition/import',
    exportTemplate: '/binDefinition/export/template',
  },
  log: {
    getApps: '/logs/getApps',
    getHosts: '/logs/getHosts',
    getInstances: '/logs/getInstances',
    queryLogs: '/logs/queryLogs',
    downloadLogs: '/logs/downloadLogs',
  },
};

export default ApiUrl;
