import qs from 'qs';
import { setSessionStorage, TOKEN_KEY } from './storage';
import router from '@/router';
import { LocationQueryRaw } from 'vue-router';

// 是否线上生产环境
const isProductOnline = () => window?.self !== window?.top;

// 当前是否为开发环境
export const isDev = process.env.NODE_ENV === 'development';

const { VITE_APP_BACKEND_USERINFO_HOST, VITE_MOCK_USERNAME, VITE_MOCK_PASSWORD } = import.meta.env;

/**
 * 手动模拟登录
 */
export const mockLogin = () => {
  fetch(`${VITE_APP_BACKEND_USERINFO_HOST}/user-center-api/login`, {
    method: 'POST',
    body: JSON.stringify({
      password: VITE_MOCK_PASSWORD,
      username: VITE_MOCK_USERNAME,
    }),
    headers: {
      'Content-Type': 'application/json',
    },
    // mode: 'no-cors'
  })
    .then((res) => res.json())
    .then((res) => {
      if (res.code === 200) {
        // 开发环境token注入
        setSessionStorage(TOKEN_KEY, res.token);
        window.location.reload();
      }
    })
    .catch((e) => {
      console.error(e);
    });
};

const getQueryString = (url: string, paraName: string) => {
  let str: string | null = null;
  const suburls = url.split('#');
  for (let i = 0; i < suburls.length; i++) {
    const suburl = suburls[i];
    const start =
      suburl.indexOf(`?${paraName}=`) < 0 ? suburl.indexOf(`&${paraName}=`) : suburl.indexOf(`?${paraName}=`);
    if (start > -1) {
      str = '';
      for (let i = start + paraName.length + 2; i < suburl.length; i++) {
        const istr = suburl.substring(i, i + 1);
        if (istr === '&') break;
        str += istr;
      }
    }
  }
  return str;
};

// 使用qs进行解析url，返回params中的所有传参
export const getUrlParams = () => {
  const url = location.href;
  const splitIndex = url.indexOf('?');
  const urlSearch = splitIndex < 0 ? '' : url.substring(splitIndex + 1);
  const searchParams = qs.parse(urlSearch, { arrayLimit: Infinity });
  return searchParams;
};

export type addTagParams = {
  title?: string; //tag显示名称
  url?: string; //跳转页面路由
  params?: string; //需要更新tag时传这个，不需要的话params直接拼在url里传过来
  isRefresh?: boolean;
  other?: string; //打开其他系统时的publicPath，例spec、onedata-dc
};

function addTag(body: addTagParams) {
  const { title, url, params, isRefresh, other = import.meta.env.VITE_APP_BASE_URL } = body;
  const obj = {
    option: 'addTag',
    tagName: title,
    activeurl: `${window.location.origin}${other}`,
    url: `${window.location.origin}${other}${url}`,
    baseUrl: '',
    isRefresh,
  };
  if (params) {
    obj.url += params;
    obj.baseUrl = `${window.location.origin}${other}${url}`;
  }
  window.parent.postMessage(obj, '*');
}

export function closeTag() {
  window.parent.postMessage(
    {
      option: 'closeTag',
    },
    '*',
  );
}

const reLogin = () => {
  // window.parent?.postMessage({ type: 're-login' }, '*');
  window.parent?.postMessage('re-login', '*');
};

// 计算字符串所占用的宽度的方法
const getTextWidth = (text: string, fontSize: number, fontWeight: string) => {
  // 创建临时元素
  const ele: HTMLElement = document.createElement('div');
  ele.style.position = 'absolute';
  ele.style.whiteSpace = 'nowrap';
  ele.style.fontSize = fontSize + 'px';
  ele.style.fontWeight = fontWeight;
  ele.innerText = text;
  document.body.append(ele);

  // 获取span的宽度
  const width: number = ele.getBoundingClientRect().width;
  // 从body中删除该span
  document.body.removeChild(ele);
  // 返回span宽度
  return width;
};

/**
 * 跳转到对应页面：支持本地和iframe场景
 * 文档：https://guwave.atlassian.net/wiki/spaces/Oneflow/pages/196083731
 */
export const jumpTo = (
  path: string,
  query: LocationQueryRaw,
  options?: Omit<addTagParams, 'url'>,
  hasSharp: boolean = true,
) => {
  if (isProductOnline()) {
    const url = `${hasSharp ? '#' : ''}${path}${query ? '?' + qs.stringify(query) : ''}`;
    addTag({
      url,
      ...options,
    });
  } else {
    router.push({ path, query });
  }
};

/* 动态计算表格列宽 */
/**
 * flexWidth
 * @param prop 每列的prop 可传''
 * @param tableData 表格数据
 * @param title 标题长内容短的，传标题  可不传
 * @param num 列中有标签等加的富余量
 * @param fontSize 表格字体的大小
 * @returns 列的宽度
 * 注：prop,title有一个必传
 */
export const getColWidth = (prop, tableData, title, num = 0, fontSize = 12) => {
  if (tableData.length === 0) {
    //表格没数据不做处理
    return;
  }
  let flexWidth = 0; //初始化表格列宽
  let columnContent = ''; //占位最宽的内容
  const canvas = document.createElement('canvas');
  const context = canvas.getContext('2d');
  context.font = `${fontSize}px Microsoft YaHei`;
  if (prop === '' && title) {
    //标题长内容少的，取标题的值,
    columnContent = title;
  } else {
    // 获取该列中占位最宽的内容
    let index = 0;
    for (let i = 0; i < tableData.length; i++) {
      const now_temp = tableData[i][prop] + '';
      const max_temp = tableData[index][prop] + '';
      const now_temp_w = context.measureText(now_temp).width;
      const max_temp_w = context.measureText(max_temp).width;
      if (now_temp_w > max_temp_w) {
        index = i;
      }
    }
    columnContent = tableData[index][prop];
    //比较占位最宽的值跟标题、标题为空的留出四个位置
    const column_w = context.measureText(columnContent).width;
    const title_w = context.measureText(title).width;
    if (column_w < title_w) {
      columnContent = title || '留四个字';
    }
  }
  // 计算最宽内容的列宽
  const width = context.measureText(columnContent);
  flexWidth = width.width + 40 + num;
  return flexWidth;
};

/**
 * 清理请求参数中的空数组
 * 如果参数值是空数组 []，则从对象中移除该参数
 * @param params 请求参数对象
 * @returns 清理后的参数对象
 */
export const cleanEmptyArrayParams = (params: Record<string, any>): Record<string, any> => {
  const cleanedParams = { ...params };

  Object.keys(cleanedParams).forEach(key => {
    const value = cleanedParams[key];
    // 如果值是数组且为空，则删除该参数
    if (Array.isArray(value) && value.length === 0) {
      delete cleanedParams[key];
    }
  });

  return cleanedParams;
};

export { getQueryString, addTag, reLogin, isProductOnline, getTextWidth };
