import { SearchParam } from '../common';
import { ReRunEnum } from '@/views/data-replay/data';

export type FileCategory = 'STDF' | 'RAW_DATA' | 'BIT_MEM' | 'WAT' | 'SUMMARY';
export type ReplayStatus = 'SUCCESS' | 'FAIL' | 'PROCESSING' | 'CREATE';

export interface DialogInfo {
  isSingle: boolean;
  replayFileMaxCount?: number;
  replayFileBatchSize?: number;
  replayType?: ReRunEnum;
  row?: DE_ListItem;
  searchParam?: SearchParam;
  uploadSelectedIds?: number[]; // 文件名上传弹窗内选中的ID列表
  isFromUploadDialog?: boolean; // 是否来自文件名上传弹窗的操作
}

//  ---------------------------- 数据入库 -------------------------------
//  数据入库下拉列表请求类型
export interface DE_DropdownParam {
  filterField?: string;
  dropDownPageType?: string;
  testArea?: string;
  factory?: string;
  deviceId?: string;
  fileCategory?: FileCategory;
  lotIdList?: string[];
  sblotIdList?: string[];
  waferNoList?: string[];
  lotType?: string;
  remoteFilePath?: string;
  testStageList?: string[];
  testProgramList?: string[];
  statusList?: string[];
  filterStatusList?: string[];
  exceptionMessage?: string[];
  exceptionMessageList?: string[];
}
//  数据入库列表请求类型
export interface DE_ListParam extends DE_DropdownParam {
  pageIndex?: number;
  pageSize?: number;
  fileName?: string;
  startTime?: string;
  endTime?: string;
  createTime?: [string, string];
  updateTime?: [string, string];
  filterCreateTime?: [string, string];
  filterUpdateTime?: [string, string];
}
//  数据入库列表项类型
export interface DE_ListItem {
  id: number;
  testArea: string;
  factory: string;
  deviceId: string;
  fileCategory: FileCategory;
  lotId: string;
  waferNo: string;
  lotType: string;
  testStage: string;
  testProgram: string;
  fileName: string;
  status: string;
  step: number;
  createTime: string;
  updateTime: string;
  replayFileMaxCount: number;
  replayFileBatchSize?: number;
}

//  ---------------------------- 数据重播 -------------------------------
//  数据重播下拉列表请求类型
export interface DR_DropdownParam {
  filterField?: string;
  dropDownPageType?: string;
  testArea?: string;
  factory?: string;
  deviceId?: string;
  fileCategory?: FileCategory;
  lotIdList?: string[];
  waferNoList?: string[];
  lotType?: string;
  testStageList?: string[];
  testProgramList?: string[];
  statusList?: string[];
  replayTypeList?: string[];
  stepList?: string[];
}
//  数据重播列表请求类型
export interface DR_ListParam extends DR_DropdownParam {
  pageIndex?: number;
  pageSize?: number;
  fileName?: string;
  startTime?: string;
  endTime?: string;
  replayStatus?: ReplayStatus[];
}
//  数据重播列表项类型
export interface DR_ListItem {
  id: number;
  testArea: string;
  factory: string;
  deviceId: string;
  fileCategory: FileCategory;
  lotId: string;
  waferNo: string;
  lotType: string;
  testStage: string;
  fileName: string;
  replayType: string;
  replayStep: number;
  failTaskCnt: number;
  successTaskCnt: number;
  totalTaskCnt: number;
  replayStatus: string;
  replayTime: string;
  replayComments: string;
  replayType: string;
  status: string;
  filterProcessStatus: string;
  step: number;
  createTime: string;
  updateTime: string;
  loading?: boolean;
}
//  单个数据重播请求类型
export interface SingleReplayParam {
  fileName?: string;
  replayType?: ReRunEnum;
  replayStep: number | undefined;
  replayComments: string | undefined;
  fileCategory: string | undefined;
  fileWarehousingRecordIdList: number[];
}
//  批量数据重播请求参数
export interface BatchReplayParam extends DE_DropdownParam {
  replayFileCnt?: number;
  replayType?: ReRunEnum;
  replayStep: number | undefined;
  replayComments: string | undefined;
  fileWarehousingRecordIdList: number[];
}

//  ---------------------------- 流程详情弹窗 -------------------------------
export interface ProcessStep {
  step: number;
  stepName: string;
  stepProcessStatus: 'SUCCESS' | 'FAIL' | 'PROCESSING' | 'CREATE' | 'CANCEL';
  startTime?: string;
  endTime?: string;
  duration?: number;
  errorMessage?: string;
  details?: string;
}

export interface FileProcessDetail {
  id: number;
  fileName: string;
  deviceId: string;
  lotId: string;
  waferNo: string;
  testArea: string;
  fileCategory: string;
  currentStep: number;
  processStatus: string;
  fileProcessStepList: ProcessStep[];
}

//  ---------------------------- 任务记录弹窗 -------------------------------
export interface TaskDropdownParam {
  filterField?: string;
  dropDownPageType?: string;
  id?: number;
  lotIdList?: string[];
  waferNoList?: string[];
  testStageList?: string[];
  statusList?: string[];
  exceptionMessageList?: string[];
}
export interface TaskListParam {
  id?: number;
  lotIdList?: string[];
  waferNoList?: string[];
  testStageList?: string[];
  statusList?: string[];
  pageIndex?: number;
  pageSize?: number;
  exceptionMessageList?: string[];
}
export interface TaskListRes {
  id: number;
  testArea: string;
  factory: string;
  deviceId: string;
  fileCategory: string;
  lotId: string;
  waferNo: string;
  lotType: string;
  testStage: string;
  replayType: string;
  step: number;
  fileList: string;
  exception: string;
  errorMessage: string;
  status: string;
  createTime: string;
  updateTime: string;
}

// 文件名列表验证响应类型
export interface FileNameValidationResponse {
  valid: boolean;
  testArea?: string;
  fileCategory?: FileCategory;
  errorMessage?: string;
  combinations?: Array<{
    testArea: string;
    fileCategory: string;
    fileCount: number;
  }>;
}
