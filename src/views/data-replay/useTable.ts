import {
  DE_SEARCH_LIST_FILE,
  DE_SEARCH_LIST_LOTWAFER,
  FilterTypeEnum,
  DE_COLUMNS,
  DE_DROPDOWN_OPTIONS,
  DR_SEARCH_LIST,
  DR_COLUMNS,
  ReRunEnum,
  StatusEnum,
} from './data';
import {
  cancelReplay,
  fileReplay,
  getDrList,
  getFileList,
  getWaferLotList,
  lotWaferReplay,
  retryReplay,
  topReplay,
} from '@/service/dataReplay';
import { SearchParam } from '@/types/common';
import { SUCCESS_CODE } from '@/utils/http';
import { BatchReplayParam, DE_ListItem, DialogInfo, DR_ListItem, SingleReplayParam } from '@/types/dataReplay';
import { cloneDeep } from 'lodash';
import { useMessage, PaginationInfo } from 'naive-ui';
import { cleanEmptyArrayParams } from '@/utils/common';

export const useTable = () => {
  enum TAB_ENUM {
    DE = 'de',
    DP = 'dp',
  }

  const deLotParams = ref<SearchParam>({});
  const deFileParams = ref<SearchParam>({});
  const drParams = ref<SearchParam>({});
  const deLotSearchRef = ref();
  const deFileSearchRef = ref();
  const drSearchRef = ref();
  const pageLoading = ref(false);
  const activeTab = ref(TAB_ENUM.DE);
  const filterType = shallowRef(FilterTypeEnum.LOTWAFER);
  const visible = ref(false);
  const taskVisible = ref(false);
  const processDetailVisible = ref(false);
  const fileWarehousingRecordId = ref<number>();
  const currentFileData = ref<DE_ListItem>();
  const message = useMessage();
  const currentId = ref<number>();
  //  选中行
  const deSelectedIds = ref<number[]>([]);

  const tableHeight = computed(() => {
    const deFileHeight = (deFileSearchRef.value?.height ?? 400) + 220;
    const deLotHeight = (deLotSearchRef.value?.height ?? 400) + 220;
    const drHeight = (drSearchRef.value?.height ?? 400) + 134;

    const deH = filterType.value === FilterTypeEnum.LOTWAFER ? deLotHeight : deFileHeight;
    return activeTab.value === TAB_ENUM.DE ? deH : drHeight;
  });

  //  批量按钮禁用
  const dropdownDis = computed(() => {
    if (!searchParam.value?.testArea || !searchParam.value?.fileCategory) {
      return true;
    } else {
      return false;
    }
  });
  //  分配搜索、表头、批量列表
  const renderObj = computed(() => {
    const deSearch = () => {
      return filterType.value === FilterTypeEnum.FILE ? DE_SEARCH_LIST_FILE : DE_SEARCH_LIST_LOTWAFER;
    };

    const searchList = activeTab.value === TAB_ENUM.DE ? deSearch() : DR_SEARCH_LIST;
    const columns =
      activeTab.value === TAB_ENUM.DE ? DE_COLUMNS(reRun, deSelectedIds, openProcessDetail) : DR_COLUMNS(openTaskDialog, retry, cancel, top);
    const dropdownOptions = DE_DROPDOWN_OPTIONS;

    return {
      columns: columns,
      searchList: searchList,
      dropdownOptions: dropdownOptions,
    };
  });
  //  弹窗信息
  const dialogInfo = reactive<DialogInfo>({
    isSingle: true,
  });
  //  搜索信息
  const searchParam = ref<SearchParam>({});
  //  数据重播表格数据
  const drTable = reactive<{ data: DR_ListItem[]; loading: boolean; pagination: any }>({
    data: [],
    loading: false,
    pagination: {
      total: 0,
      pageSize: 20,
      pageIndex: 1,
      prefix: (info: PaginationInfo) => {
        return `共${info.itemCount}条`;
      },
      onChange: (page: number) => {
        drTable.pagination.pageIndex = page;
        getList({ ...searchParam.value, pageIndex: page });
      },
      onUpdatePageSize: (pageSize: number) => {
        drTable.pagination.pageIndex = 1;
        drTable.pagination.pageSize = pageSize;
        getList({ ...searchParam.value, pageSize: pageSize, pageIndex: 1 });
      },
    },
  });
  //  数据入库表格数据
  const deTable = reactive<{ data: DE_ListItem[]; loading: boolean; pagination: any }>({
    data: [],
    loading: false,
    pagination: {
      total: 0,
      pageSize: 20,
      pageIndex: 1,
      prefix: (info: PaginationInfo) => {
        return `共${info.itemCount}条`;
      },
      onChange: (page: number) => {
        deTable.pagination.pageIndex = page;
        getList({ ...searchParam.value, pageIndex: page });
      },
      onUpdatePageSize: (pageSize: number) => {
        deTable.pagination.pageIndex = 1;
        deTable.pagination.pageSize = pageSize;
        getList({ ...searchParam.value, pageSize: pageSize, pageIndex: 1 });
      },
    },
  });

  //  tab切换
  const tabChange = (tab: TAB_ENUM) => {
    activeTab.value = tab;
    getList(tab === TAB_ENUM.DE ? deLotParams.value : drParams.value);
    // searchRef.value.reset();
  };
  // filterType切换
  const filterChange = () => {
    searchParam.value = {};
    getList(filterType.value === FilterTypeEnum.LOTWAFER ? deLotParams.value : deFileParams.value);

    // if (filterType.value === FilterTypeEnum.LOTWAFER) {
    //   deSearchRef.value?.reset();
    // }
  };
  // 单个重跑
  const reRun = (row: DE_ListItem, key: ReRunEnum) => {
    dialogInfo.isSingle = true;
    dialogInfo.replayType = key;
    dialogInfo.row = row;
    dialogInfo.replayFileBatchSize = row.replayFileBatchSize ?? 1000;
    visible.value = true;
  };
  // 批量操作
  const dropdownSelected = (key: ReRunEnum) => {
    if (deSelectedIds.value?.length > 0) {
      if (
        deSelectedIds.value?.some((id) => {
          const row = deTable.data?.find((ite) => ite.id === id);
          if (row) {
            return row.status !== StatusEnum.SUCCESS && row.status !== StatusEnum.FAIL;
          } else {
            return false;
          }
        })
      ) {
        message.warning('选中行中存在无效数据!');
      } else {
        dialogInfo.isSingle = false;
        dialogInfo.replayType = key;
        dialogInfo.searchParam = searchParam.value;
        dialogInfo.replayFileMaxCount = deTable.pagination.total;
        // 从第一行数据获取replayFileBatchSize，如果没有则使用默认值
        dialogInfo.replayFileBatchSize = deTable.data?.[0]?.replayFileBatchSize ?? 1000;

        visible.value = true;
      }
    } else {
      if (deTable.data?.some((item) => item.status !== StatusEnum.SUCCESS && item.status !== StatusEnum.FAIL)) {
        message.warning('表格中存在无效数据!');
      } else {
        dialogInfo.isSingle = false;
        dialogInfo.replayType = key;
        dialogInfo.searchParam = searchParam.value;
        dialogInfo.replayFileMaxCount = deTable.pagination.total;
        // 从第一行数据获取replayFileBatchSize，如果没有则使用默认值
        dialogInfo.replayFileBatchSize = deTable.data?.[0]?.replayFileBatchSize ?? 1000;

        visible.value = true;
      }
    }
  };

  //  重跑提交
  const reRunSubmit = async (params: SingleReplayParam | BatchReplayParam) => {
    pageLoading.value = true;

    const len = deSelectedIds.value?.length === 0 ? deTable.pagination.total : deSelectedIds.value?.length;

    //  单个时需要传行的testArea和fileCategory
    let res;
    // 清理空数组参数
    const cleanedSearchParam = cleanEmptyArrayParams(searchParam.value);
    if (dialogInfo.isSingle) {
      res =
        filterType.value === FilterTypeEnum.LOTWAFER
          ? await lotWaferReplay({
              ...params,
              ...cleanedSearchParam,
              testArea: dialogInfo.row?.testArea,
              fileCategory: dialogInfo.row?.fileCategory,
              replayFileCnt: 1,
            } as BatchReplayParam)
          : await fileReplay({
              ...params,
              ...cleanedSearchParam,
              testArea: dialogInfo.row?.testArea,
              fileCategory: dialogInfo.row?.fileCategory,
              replayFileCnt: 1,
            } as BatchReplayParam);
    } else {
      res =
        filterType.value === FilterTypeEnum.LOTWAFER
          ? await lotWaferReplay({
              ...params,
              ...cleanedSearchParam,
              replayFileCnt: len,
            } as BatchReplayParam)
          : await fileReplay({
              ...params,
              ...cleanedSearchParam,
              replayFileCnt: len,
            } as BatchReplayParam);
    }

    pageLoading.value = false;
    if (res.data.code === SUCCESS_CODE) {
      message.success('重试成功!');
      visible.value = false;
      return true;
    }
    return false;
  };

  //  获取列表
  const getList = async (param: SearchParam) => {
    // 清理空数组参数
    const cleanedParam = cleanEmptyArrayParams(param);
    searchParam.value = cloneDeep(cleanedParam);
    if (activeTab.value === TAB_ENUM.DE) {
      if (filterType.value === FilterTypeEnum.LOTWAFER) {
        deLotParams.value = cleanedParam;
      } else {
        deFileParams.value = cleanedParam;
      }

      deTable.loading = true;
      const res = filterType.value === FilterTypeEnum.FILE ? await getFileList(cleanedParam) : await getWaferLotList(cleanedParam);
      deTable.loading = false;

      if (res.data.code === SUCCESS_CODE) {
        const rData = res.data.data;
        deTable.data = rData.data;

        deTable.pagination.total = rData.total;
        deTable.pagination.pageIndex = rData.pageIndex;
        deTable.pagination.pageSize = rData.pageSize;
      }
    } else {
      drParams.value = cleanedParam;

      drTable.loading = true;
      const res = await getDrList(cleanedParam);
      drTable.loading = false;

      if (res.data.code === SUCCESS_CODE) {
        const rData = res.data.data;
        drTable.data = rData.data;

        drTable.pagination.total = rData.total;
        drTable.pagination.pageIndex = rData.pageIndex;
        drTable.pagination.pageSize = rData.pageSize;
      }
    }

    deSelectedIds.value = [];
  };
  //  打开任务记录
  const openTaskDialog = (row: DR_ListItem) => {
    currentId.value = row.id;
    taskVisible.value = true;
  };
  //  打开流程详情
  const openProcessDetail = (row: DE_ListItem) => {
    fileWarehousingRecordId.value = row.id;
    currentFileData.value = row;
    processDetailVisible.value = true;
  };
  //  重试
  const retry = async (id: number) => {
    drTable.data?.map((item) => {
      if (item.id === id) item.loading = true;
    });

    const res = await retryReplay(id);
    if (res.data.code === SUCCESS_CODE) {
      message.success('执行成功!');
      getList(searchParam.value);
    }
  };
  //  取消
  const cancel = async (id: number) => {
    const res = await cancelReplay(id);
    if (res.data.code === SUCCESS_CODE) {
      message.success('取消成功!');
      getList(searchParam.value);
    }
  };

  //  置顶
  const top = async (id: number) => {
    const res = await topReplay(id);
    if (res.data.code === SUCCESS_CODE) {
      message.success('置顶成功!');
      getList(searchParam.value);
    }
  };

  return {
    cancel,
    top,
    drTable,
    deTable,
    visible,
    getList,
    TAB_ENUM,
    currentId,
    renderObj,
    activeTab,
    tabChange,
    dialogInfo,
    filterType,
    pageLoading,
    dropdownDis,
    taskVisible,
    reRunSubmit,
    searchParam,
    tableHeight,
    drSearchRef,
    filterChange,
    deSelectedIds,
    deFileSearchRef,
    deLotSearchRef,
    dropdownSelected,
    fileWarehousingRecordId,
    currentFileData,
    processDetailVisible,
    openProcessDetail,
  };
};
