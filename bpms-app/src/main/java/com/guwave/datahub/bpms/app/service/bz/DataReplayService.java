package com.guwave.datahub.bpms.app.service.bz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.guwave.datahub.bpms.app.exception.BpmsException;
import com.guwave.datahub.bpms.app.util.JsonUtil;
import com.guwave.datahub.bpms.app.vo.bz.replay.request.*;
import com.guwave.datahub.bpms.app.vo.bz.replay.response.*;
import com.guwave.datahub.bpms.app.vo.web.PageableDataVo;
import com.guwave.datahub.bpms.app.vo.web.RequestContext;
import com.guwave.datahub.bpms.app.web.response.ResponseCode;
import com.guwave.datahub.bpms.common.constant.Constant;
import com.guwave.datahub.bpms.dao.domain.replay.DataReplayRecord;
import com.guwave.datahub.bpms.dao.repository.bpms.DataReplayRecordRepository;
import com.guwave.datahub.bpms.dao.repository.dw.CleanupTaskRepository;
import com.guwave.datahub.bpms.dao.repository.dw.FileLoadingLogRepository;
import com.guwave.datahub.bpms.dao.repository.dw.FileWarehousingRecordRepository;
import com.guwave.datahub.bpms.dao.repository.dw.LayerCalculatePoolRepository;
import com.guwave.datahub.bpms.dao.repository.linkx.BzAppInstanceRecordRepository;
import com.guwave.datahub.bpms.dao.repository.linkx.BzAppInstanceRepository;
import com.guwave.datahub.bpms.dao.vo.dw.FileProcessStepVo;
import com.guwave.datahub.bpms.dao.vo.dw.FileWarehousingMinVo;
import com.guwave.onedata.dataware.bridge.api.iface.IDataRepairRpcService;
import com.guwave.onedata.dataware.bridge.api.vo.request.ReplayTaskVo;
import com.guwave.onedata.dataware.bridge.api.vo.request.ReplayVo;
import com.guwave.onedata.dataware.bridge.api.vo.response.ReplayMessage;
import com.guwave.onedata.dataware.common.contant.*;
import com.guwave.onedata.dataware.dao.mysql.domain.dw.*;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.LotMetaDataDetailRepository;
import com.guwave.onedata.dataware.dao.mysql.repository.dw.SftpFileDetailRepository;
import com.guwave.onedata.linkx.common.constant.OptType;
import com.guwave.onedata.linkx.dao.mysql.domain.bz.BzAppInstance;
import io.swagger.v3.core.util.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.guwave.datahub.bpms.app.web.response.ResponseCode.Business.*;
import static com.guwave.datahub.bpms.app.web.response.ResponseCode.Common.COMMON_OPERATE_NOT_PRESENT;

/**
 * Copyright (C), 2024, guwave
 * <p>
 * DataReplayService
 *
 * <AUTHOR> Qian
 * @version 0.0.1
 * 2024-02-26 13:34:45
 */
@Slf4j
@Service
public class DataReplayService {

    @DubboReference
    private IDataRepairRpcService dataRepairRpcService;

    @Autowired
    private FileWarehousingRecordRepository fileWarehousingRecordRepository;
    @Autowired
    private DataReplayRecordRepository dataReplayRecordRepository;
    @Autowired
    private CleanupTaskRepository cleanupTaskRepository;
    @Autowired
    private BzAppInstanceRecordRepository bzAppInstanceRecordRepository;
    @Autowired
    private BzAppInstanceRepository bzAppInstanceRepository;
    @Autowired
    private SftpFileDetailRepository sftpFileDetailRepository;
    @Autowired
    private LotMetaDataDetailRepository lotMetaDataDetailRepository;
    @Autowired
    private LayerCalculatePoolRepository layerCalculatePoolRepository;
    @Autowired
    private FileLoadingLogRepository fileLoadingLogRepository;

    @Value("${spring.data.replay.file.maxCount}")
    private Integer replayFileMaxCount;
    @Value("${spring.data.replay.file.batchSize}")
    private Integer replayFileBatchSize;
    @Value("${spring.data.replay.task.maxCount}")
    private Integer replayTaskMaxCount;

    final List<String> DATA_REPLAY_SUPPORT_FILE_CATEGORY = Arrays.asList(
            FileCategory.WAT.getCategory(),
            FileCategory.SUMMARY.getCategory(),
            FileCategory.RAW_DATA.getCategory(),
            FileCategory.BIT_MEM.getCategory(),
            FileCategory.STDF.getCategory()
    );

    public List<DataReplayDropDownVo> filterLotWaferDropDown(DataReplayLotWaferDropDownFilterVo dataReplayLotWaferDropDownFilterVo) {
        // 处理Factory字段：如果包含(ALL)，则不过滤Factory
        List<String> factoryList = dataReplayLotWaferDropDownFilterVo.getFactoryList();
        if (factoryList != null && factoryList.contains("(ALL)")) {
            factoryList = null;
        }

        List<String> fieldResult = fileWarehousingRecordRepository.findDynamicField(
                dataReplayLotWaferDropDownFilterVo.getFilterField(),
                RequestContext.getCustomer(),
                dataReplayLotWaferDropDownFilterVo.getTestArea(),
                factoryList,
                dataReplayLotWaferDropDownFilterVo.getDeviceIdList(),
                FileCategory.of(dataReplayLotWaferDropDownFilterVo.getFileCategory()),
                dataReplayLotWaferDropDownFilterVo.getLotIdList(),
                dataReplayLotWaferDropDownFilterVo.getWaferNoList(),
                dataReplayLotWaferDropDownFilterVo.getSblotIdList(),
                dataReplayLotWaferDropDownFilterVo.getLotType(),
                dataReplayLotWaferDropDownFilterVo.getTestStageList(),
                dataReplayLotWaferDropDownFilterVo.getTestProgramList(),
                dataReplayLotWaferDropDownFilterVo.getStepList(),
                dataReplayLotWaferDropDownFilterVo.getProcessStatusList(),
                dataReplayLotWaferDropDownFilterVo.getExceptionMessage(),
                CollectionUtils.isEmpty(dataReplayLotWaferDropDownFilterVo.getCreateTime()) ? null : dataReplayLotWaferDropDownFilterVo.getCreateTime().get(0),
                CollectionUtils.isEmpty(dataReplayLotWaferDropDownFilterVo.getCreateTime()) ? null : dataReplayLotWaferDropDownFilterVo.getCreateTime().get(1),
                CollectionUtils.isEmpty(dataReplayLotWaferDropDownFilterVo.getUpdateTime()) ? null : dataReplayLotWaferDropDownFilterVo.getUpdateTime().get(0),
                CollectionUtils.isEmpty(dataReplayLotWaferDropDownFilterVo.getUpdateTime()) ? null : dataReplayLotWaferDropDownFilterVo.getUpdateTime().get(1),
                DATA_REPLAY_SUPPORT_FILE_CATEGORY);

        fieldResult = fieldResult.stream()
                .map(this::wrapperBlankString)
                .distinct()
                .collect(Collectors.toList());

        log.info("下拉框结果 field:{}, value:{}", dataReplayLotWaferDropDownFilterVo.getFilterField(), String.join(Constant.COMMA, fieldResult));
        return DataReplayDropDownVo.ofList(fieldResult);
    }

    public List<DataReplayDropDownVo> filterFileDropDown(DataReplayFileDropDownFilterVo dataReplayFileDropDownFilterVo) {
        log.info("filterDropDown");
        // 处理Factory字段：如果包含(ALL)，则不过滤Factory
        List<String> factoryList = dataReplayFileDropDownFilterVo.getFactoryList();
        if (factoryList != null && factoryList.contains("(ALL)")) {
            factoryList = null;
        }

        List<String> fieldResult = fileWarehousingRecordRepository.findDynamicFieldByFile(
                dataReplayFileDropDownFilterVo.getFilterField(),
                RequestContext.getCustomer(),
                dataReplayFileDropDownFilterVo.getTestArea(),
                factoryList,
                FileCategory.of(dataReplayFileDropDownFilterVo.getFileCategory()),
                dataReplayFileDropDownFilterVo.getStepList(),
                dataReplayFileDropDownFilterVo.getProcessStatusList(),
                dataReplayFileDropDownFilterVo.getExceptionMessage(),
                CollectionUtils.isEmpty(dataReplayFileDropDownFilterVo.getCreateTime()) ? null : dataReplayFileDropDownFilterVo.getCreateTime().get(0),
                CollectionUtils.isEmpty(dataReplayFileDropDownFilterVo.getCreateTime()) ? null : dataReplayFileDropDownFilterVo.getCreateTime().get(1),
                CollectionUtils.isEmpty(dataReplayFileDropDownFilterVo.getUpdateTime()) ? null : dataReplayFileDropDownFilterVo.getUpdateTime().get(0),
                CollectionUtils.isEmpty(dataReplayFileDropDownFilterVo.getUpdateTime()) ? null : dataReplayFileDropDownFilterVo.getUpdateTime().get(1),
                DATA_REPLAY_SUPPORT_FILE_CATEGORY,
                dataReplayFileDropDownFilterVo.getTestProgramList());
        fieldResult = fieldResult.stream()
                .map(this::wrapperBlankString)
                .distinct()
                .collect(Collectors.toList());

        log.info("下拉框结果 field:{}, value:{}", dataReplayFileDropDownFilterVo.getFilterField(), String.join(Constant.COMMA, fieldResult));
        return DataReplayDropDownVo.ofList(fieldResult);
    }

    public List<DataReplayDropDownVo> filterReplayRecordDropDown(DataReplayRecordDropDownFilterVo dataReplayRecordDropDownFilterVo) {
        // 处理Factory字段：如果包含(ALL)，则不过滤Factory
        List<String> factoryList = dataReplayRecordDropDownFilterVo.getFactoryList();
        if (factoryList != null && factoryList.contains("(ALL)")) {
            factoryList = null;
        }

        List<String> fieldResult = dataReplayRecordRepository.findDynamicField(
                dataReplayRecordDropDownFilterVo.getFilterField(),
                RequestContext.getCustomer(),
                dataReplayRecordDropDownFilterVo.getTestArea(),
                factoryList == null ? null : JSON.toJSONString(factoryList),
                dataReplayRecordDropDownFilterVo.getDeviceIdList() == null ? null : JSON.toJSONString(dataReplayRecordDropDownFilterVo.getDeviceIdList()),
                FileCategory.of(dataReplayRecordDropDownFilterVo.getFileCategory()),
                dataReplayRecordDropDownFilterVo.getLotIdList() == null ? null : JSON.toJSONString(dataReplayRecordDropDownFilterVo.getLotIdList()),
                dataReplayRecordDropDownFilterVo.getWaferNoList() == null ? null : JSON.toJSONString(dataReplayRecordDropDownFilterVo.getWaferNoList()),
                dataReplayRecordDropDownFilterVo.getSblotIdList() == null ? null : JSON.toJSONString(dataReplayRecordDropDownFilterVo.getSblotIdList()),
                dataReplayRecordDropDownFilterVo.getLotType(),
                dataReplayRecordDropDownFilterVo.getTestStageList() == null ? null : JSON.toJSONString(dataReplayRecordDropDownFilterVo.getTestStageList()),
                dataReplayRecordDropDownFilterVo.getTestProgramList() == null ? null : JSON.toJSONString(dataReplayRecordDropDownFilterVo.getTestProgramList()),
                dataReplayRecordDropDownFilterVo.getReplayTypeList(),
                dataReplayRecordDropDownFilterVo.getStepList(),
                dataReplayRecordDropDownFilterVo.getFilterProcessStatusList() == null ? null : JSON.toJSONString(dataReplayRecordDropDownFilterVo.getFilterProcessStatusList()),
                dataReplayRecordDropDownFilterVo.getExceptionMessageList(),
                CollectionUtils.isEmpty(dataReplayRecordDropDownFilterVo.getFilterCreateTime()) ? null : dataReplayRecordDropDownFilterVo.getFilterCreateTime().get(0),
                CollectionUtils.isEmpty(dataReplayRecordDropDownFilterVo.getFilterCreateTime()) ? null : dataReplayRecordDropDownFilterVo.getFilterCreateTime().get(1),
                CollectionUtils.isEmpty(dataReplayRecordDropDownFilterVo.getFilterUpdateTime()) ? null : dataReplayRecordDropDownFilterVo.getFilterUpdateTime().get(0),
                CollectionUtils.isEmpty(dataReplayRecordDropDownFilterVo.getFilterUpdateTime()) ? null : dataReplayRecordDropDownFilterVo.getFilterUpdateTime().get(1),
                CollectionUtils.isEmpty(dataReplayRecordDropDownFilterVo.getCreateTime()) ? null : dataReplayRecordDropDownFilterVo.getCreateTime().get(0),
                CollectionUtils.isEmpty(dataReplayRecordDropDownFilterVo.getCreateTime()) ? null : dataReplayRecordDropDownFilterVo.getCreateTime().get(1),
                CollectionUtils.isEmpty(dataReplayRecordDropDownFilterVo.getUpdateTime()) ? null : dataReplayRecordDropDownFilterVo.getUpdateTime().get(0),
                CollectionUtils.isEmpty(dataReplayRecordDropDownFilterVo.getUpdateTime()) ? null : dataReplayRecordDropDownFilterVo.getUpdateTime().get(1),
                dataReplayRecordDropDownFilterVo.getProcessStatusList()

        );
        fieldResult = fieldResult.stream()
                .map(this::wrapperBlankString)
                .flatMap(field -> JsonUtil.isJSONArray(field) ? JSONArray.parseArray(field, String.class).stream() : Stream.of(field))
                .distinct()
                .collect(Collectors.toList());

        log.info("下拉框结果 field:{}, value:{}", dataReplayRecordDropDownFilterVo.getFilterField(), String.join(Constant.COMMA, fieldResult));
        return DataReplayDropDownVo.ofList(fieldResult);
    }


    public List<DataReplayDropDownVo> filterReplayTaskDropDown(DataReplayTaskDropDownFilterVo dataReplayTaskDropDownFilterVo) {
        List<String> fieldResult = cleanupTaskRepository.findDynamicField(
                dataReplayTaskDropDownFilterVo.getId(), RequestContext.getCustomer(), dataReplayTaskDropDownFilterVo.getLotIdList(), dataReplayTaskDropDownFilterVo.getWaferNoList(),
                dataReplayTaskDropDownFilterVo.getTestStageList(), dataReplayTaskDropDownFilterVo.getExceptionMessageList(), dataReplayTaskDropDownFilterVo.getStatusList(), dataReplayTaskDropDownFilterVo.getFilterField());
        fieldResult = fieldResult.stream()
                .filter(StringUtils::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());
        log.info("下拉框结果 field:{}, value:{}", dataReplayTaskDropDownFilterVo.getFilterField(), String.join(Constant.COMMA, fieldResult));
        return DataReplayDropDownVo.ofList(fieldResult);
    }


    /**
     * 查询入库记录
     *
     * @param dataReplayWarehousingRecordQueryVo 查询条件对象，包含分页信息和过滤条件
     * @return PageableDataVo<DataReplayWarehousingRecordVo> 分页数据对象，包含入库记录的详细信息
     */
    public PageableDataVo<DataReplayWarehousingRecordVo> queryWarehousingLotWafer(DataReplayLotWaferQueryVo dataReplayWarehousingRecordQueryVo) {
        Pageable pageable = PageRequest.of(dataReplayWarehousingRecordQueryVo.getJpaPageIndex(), dataReplayWarehousingRecordQueryVo.getPageSize(), Sort.by(Sort.Direction.DESC, "update_time"));

        // 处理Factory字段：如果包含(ALL)，则不过滤Factory
        List<String> factoryList = dataReplayWarehousingRecordQueryVo.getFactoryList();
        if (factoryList != null && factoryList.contains("(ALL)")) {
            factoryList = null;
        }

        Page<FileWarehousingRecord> fileWarehousingRecordPage = fileWarehousingRecordRepository.findWarehousingFileByLotWaferFilters(
                RequestContext.getCustomer(),
                dataReplayWarehousingRecordQueryVo.getTestArea(),
                factoryList,
                dataReplayWarehousingRecordQueryVo.getDeviceIdList(),
                FileCategory.of(dataReplayWarehousingRecordQueryVo.getFileCategory()),
                dataReplayWarehousingRecordQueryVo.getLotIdList(),
                dataReplayWarehousingRecordQueryVo.getWaferNoList(),
                dataReplayWarehousingRecordQueryVo.getSblotIdList(),
                dataReplayWarehousingRecordQueryVo.getLotType(),
                dataReplayWarehousingRecordQueryVo.getTestStageList(),
                dataReplayWarehousingRecordQueryVo.getTestProgramList(),
                dataReplayWarehousingRecordQueryVo.getStepList(),
                dataReplayWarehousingRecordQueryVo.getProcessStatusList(),
                dataReplayWarehousingRecordQueryVo.getExceptionMessage(),
                CollectionUtils.isEmpty(dataReplayWarehousingRecordQueryVo.getCreateTime()) ? null : dataReplayWarehousingRecordQueryVo.getCreateTime().get(0),
                CollectionUtils.isEmpty(dataReplayWarehousingRecordQueryVo.getCreateTime()) ? null : dataReplayWarehousingRecordQueryVo.getCreateTime().get(1),
                CollectionUtils.isEmpty(dataReplayWarehousingRecordQueryVo.getUpdateTime()) ? null : dataReplayWarehousingRecordQueryVo.getUpdateTime().get(0),
                CollectionUtils.isEmpty(dataReplayWarehousingRecordQueryVo.getUpdateTime()) ? null : dataReplayWarehousingRecordQueryVo.getUpdateTime().get(1),
                DATA_REPLAY_SUPPORT_FILE_CATEGORY,
                dataReplayWarehousingRecordQueryVo.getFileNameList(),
                pageable);
        return PageableDataVo.of(fileWarehousingRecordPage.map(data -> DataReplayWarehousingRecordVo.of(data, replayFileMaxCount, replayFileBatchSize)));
    }

    /**
     * 查询入库记录
     *
     * @param dataReplayWarehousingRecordQueryVo 查询条件对象，包含分页信息和过滤条件
     * @return PageableDataVo<DataReplayWarehousingRecordVo> 分页数据对象，包含入库记录的详细信息
     */
    public PageableDataVo<DataReplayWarehousingRecordVo> queryWarehousingRecordByFile(DataReplayFileQueryVo dataReplayWarehousingRecordQueryVo) {
        Pageable pageable = PageRequest.of(dataReplayWarehousingRecordQueryVo.getJpaPageIndex(), dataReplayWarehousingRecordQueryVo.getPageSize(), Sort.by(Sort.Direction.DESC, "update_time"));

        // 处理Factory字段：如果包含(ALL)，则不过滤Factory
        List<String> factoryList = dataReplayWarehousingRecordQueryVo.getFactoryList();
        if (factoryList != null && factoryList.contains("(ALL)")) {
            factoryList = null;
        }

        Page<FileWarehousingRecord> fileWarehousingRecordPage = fileWarehousingRecordRepository.findWarehousingFileByFileFilters(
                RequestContext.getCustomer(),
                dataReplayWarehousingRecordQueryVo.getFileName(),
                dataReplayWarehousingRecordQueryVo.getRemoteFilePath(),
                dataReplayWarehousingRecordQueryVo.getTestArea(),
                factoryList,
                FileCategory.of(dataReplayWarehousingRecordQueryVo.getFileCategory()),
                dataReplayWarehousingRecordQueryVo.getStepList(),
                dataReplayWarehousingRecordQueryVo.getProcessStatusList(),
                dataReplayWarehousingRecordQueryVo.getExceptionMessage(),
                CollectionUtils.isEmpty(dataReplayWarehousingRecordQueryVo.getCreateTime()) ? null : dataReplayWarehousingRecordQueryVo.getCreateTime().get(0),
                CollectionUtils.isEmpty(dataReplayWarehousingRecordQueryVo.getCreateTime()) ? null : dataReplayWarehousingRecordQueryVo.getCreateTime().get(1),
                CollectionUtils.isEmpty(dataReplayWarehousingRecordQueryVo.getUpdateTime()) ? null : dataReplayWarehousingRecordQueryVo.getUpdateTime().get(0),
                CollectionUtils.isEmpty(dataReplayWarehousingRecordQueryVo.getUpdateTime()) ? null : dataReplayWarehousingRecordQueryVo.getUpdateTime().get(1),
                DATA_REPLAY_SUPPORT_FILE_CATEGORY,
                dataReplayWarehousingRecordQueryVo.getTestProgramList(),
                pageable);
        return PageableDataVo.of(fileWarehousingRecordPage.map(data -> DataReplayWarehousingRecordVo.of(data, replayFileMaxCount, replayFileBatchSize)));
    }

    public Void batchReplay(DataReplayLotWaferOperateVo dataReplayLotWaferOperateVo) {
        if (dataReplayRecordRepository.countByCustomerAndProcessStatusIn(RequestContext.getCustomer(), Arrays.asList(ProcessStatus.CREATE, ProcessStatus.PROCESSING)) >= replayTaskMaxCount) {
            throw new BpmsException(DATA_REPLAY_TOO_MANY_TASK);
        }
        String fileWarehousingRecordIdsFilterStr = CollectionUtils.isEmpty(dataReplayLotWaferOperateVo.getFileWarehousingRecordIdList()) ? Constant.EMPTY :
                dataReplayLotWaferOperateVo.getFileWarehousingRecordIdList().stream().map(String::valueOf).sorted().collect(Collectors.joining(Constant.COMMA));

        // 处理Factory字段：如果包含(ALL)，则不过滤Factory，同时不保存(ALL)到数据库
        List<String> factoryList = dataReplayLotWaferOperateVo.getFactoryList();
        List<String> factoryListForQuery = factoryList;
        List<String> factoryListForSave = factoryList;
        if (factoryList != null && factoryList.contains("(ALL)")) {
            factoryListForQuery = null;  // 查询时不过滤
            factoryListForSave = null;   // 保存时不存储(ALL)
        }

        List<Long> fileWarehousingRecordIds = dataReplayLotWaferOperateVo.getFileWarehousingRecordIdList();
        if (CollectionUtils.isEmpty(fileWarehousingRecordIds)) {
            fileWarehousingRecordIds = fileWarehousingRecordRepository.findFileWarehousingRecordIdByLotWaferFilters(
                    RequestContext.getCustomer(),
                    dataReplayLotWaferOperateVo.getTestArea(),
                    factoryListForQuery,
                    dataReplayLotWaferOperateVo.getDeviceIdList(),
                    FileCategory.of(dataReplayLotWaferOperateVo.getFileCategory()),
                    dataReplayLotWaferOperateVo.getLotIdList(),
                    dataReplayLotWaferOperateVo.getWaferNoList(),
                    dataReplayLotWaferOperateVo.getSblotIdList(),
                    dataReplayLotWaferOperateVo.getLotType(),
                    dataReplayLotWaferOperateVo.getTestStageList(),
                    dataReplayLotWaferOperateVo.getTestProgramList(),
                    dataReplayLotWaferOperateVo.getStepList(),
                    dataReplayLotWaferOperateVo.getProcessStatusList(),
                    dataReplayLotWaferOperateVo.getExceptionMessage(),
                    CollectionUtils.isEmpty(dataReplayLotWaferOperateVo.getCreateTime()) ? null : dataReplayLotWaferOperateVo.getCreateTime().get(0),
                    CollectionUtils.isEmpty(dataReplayLotWaferOperateVo.getCreateTime()) ? null : dataReplayLotWaferOperateVo.getCreateTime().get(1),
                    CollectionUtils.isEmpty(dataReplayLotWaferOperateVo.getUpdateTime()) ? null : dataReplayLotWaferOperateVo.getUpdateTime().get(0),
                    CollectionUtils.isEmpty(dataReplayLotWaferOperateVo.getUpdateTime()) ? null : dataReplayLotWaferOperateVo.getUpdateTime().get(1),
                    DATA_REPLAY_SUPPORT_FILE_CATEGORY);
        }

        preCheck(dataReplayLotWaferOperateVo.getFileCategory(), CleanUpType.of(dataReplayLotWaferOperateVo.getReplayType()),
                StepType.of(dataReplayLotWaferOperateVo.getReplayStep()), fileWarehousingRecordIds);

        // 保存replay记录
        DataReplayRecord dataReplayRecord = DataReplayRecord.builder()
                .customer(RequestContext.getCustomer())
                .testArea(TestArea.of(dataReplayLotWaferOperateVo.getTestArea()))
                .factory(factoryListForSave)
                .deviceId(dataReplayLotWaferOperateVo.getDeviceIdList())
                .fileCategory(dataReplayLotWaferOperateVo.getFileCategory())
                .lotIdList(dataReplayLotWaferOperateVo.getLotIdList())
                .waferNoList(dataReplayLotWaferOperateVo.getWaferNoList())
                .sblotIdList(dataReplayLotWaferOperateVo.getSblotIdList())
                .lotType(LotType.of(dataReplayLotWaferOperateVo.getLotType()))
                .testStageList(dataReplayLotWaferOperateVo.getTestStageList())
                .testProgramList(dataReplayLotWaferOperateVo.getTestProgramList())
                .replayType(CleanUpType.of(dataReplayLotWaferOperateVo.getReplayType()))
                .step(StepType.of(dataReplayLotWaferOperateVo.getReplayStep()))
                .filterProcessStatusList(dataReplayLotWaferOperateVo.getProcessStatusList())
                .filterExceptionMessage(dataReplayLotWaferOperateVo.getExceptionMessage())
                .filterFileWarehousingRecordId(fileWarehousingRecordIdsFilterStr)
                .filterStepList(dataReplayLotWaferOperateVo.getStepList())
                .startCreateTime(CollectionUtils.isEmpty(dataReplayLotWaferOperateVo.getCreateTime()) ? null : dataReplayLotWaferOperateVo.getCreateTime().get(0))
                .endCreateTime(CollectionUtils.isEmpty(dataReplayLotWaferOperateVo.getCreateTime()) ? null : dataReplayLotWaferOperateVo.getCreateTime().get(1))
                .startUpdateTime(CollectionUtils.isEmpty(dataReplayLotWaferOperateVo.getUpdateTime()) ? null : dataReplayLotWaferOperateVo.getUpdateTime().get(0))
                .endUpdateTime(CollectionUtils.isEmpty(dataReplayLotWaferOperateVo.getUpdateTime()) ? null : dataReplayLotWaferOperateVo.getUpdateTime().get(1))
                .fileWarehousingRecordId(fileWarehousingRecordIds.stream().map(String::valueOf).distinct().sorted().collect(Collectors.joining(Constant.COMMA)))
                .replayComments(dataReplayLotWaferOperateVo.getReplayComments())
                .replayFileCnt(fileWarehousingRecordIds.size())
                .totalTaskCnt(0)
                .successTaskCnt(0)
                .failTaskCnt(0)
                .processStatus(ProcessStatus.CREATE)
                .createTime(new Date())
                .updateTime(new Date())
                .createUser(RequestContext.getUserName())
                .updateUser(RequestContext.getUserName())
                .build();
        this.saveDataReplayRecord(dataReplayRecord, fileWarehousingRecordIds);
        return null;
    }

    public Void batchReplay(DataReplayFileOperateVo dataReplayFileOperateVo) {
        if (dataReplayRecordRepository.countByCustomerAndProcessStatusIn(RequestContext.getCustomer(), Arrays.asList(ProcessStatus.CREATE, ProcessStatus.PROCESSING)) >= replayTaskMaxCount) {
            throw new BpmsException(DATA_REPLAY_TOO_MANY_TASK);
        }
        String fileWarehousingRecordIdsFilterStr = CollectionUtils.isEmpty(dataReplayFileOperateVo.getFileWarehousingRecordIdList()) ? Constant.EMPTY :
                dataReplayFileOperateVo.getFileWarehousingRecordIdList().stream().map(String::valueOf).sorted().collect(Collectors.joining(Constant.COMMA));

        // 处理Factory字段：如果包含(ALL)，则不过滤Factory，同时不保存(ALL)到数据库
        List<String> factoryList = dataReplayFileOperateVo.getFactoryList();
        List<String> factoryListForQuery = factoryList;
        List<String> factoryListForSave = factoryList;
        if (factoryList != null && factoryList.contains("(ALL)")) {
            factoryListForQuery = null;  // 查询时不过滤
            factoryListForSave = null;   // 保存时不存储(ALL)
        }

        List<Long> fileWarehousingRecordIds = dataReplayFileOperateVo.getFileWarehousingRecordIdList();
        if (CollectionUtils.isEmpty(fileWarehousingRecordIds)) {
            fileWarehousingRecordIds = fileWarehousingRecordRepository.findWarehousingRecordIdByFileFilters(
                    RequestContext.getCustomer(),
                    dataReplayFileOperateVo.getFileName(),
                    dataReplayFileOperateVo.getRemoteFilePath(),
                    dataReplayFileOperateVo.getTestArea(),
                    factoryListForQuery,
                    FileCategory.of(dataReplayFileOperateVo.getFileCategory()),
                    dataReplayFileOperateVo.getStepList(),
                    dataReplayFileOperateVo.getProcessStatusList(),
                    dataReplayFileOperateVo.getExceptionMessage(),
                    CollectionUtils.isEmpty(dataReplayFileOperateVo.getCreateTime()) ? null : dataReplayFileOperateVo.getCreateTime().get(0),
                    CollectionUtils.isEmpty(dataReplayFileOperateVo.getCreateTime()) ? null : dataReplayFileOperateVo.getCreateTime().get(1),
                    CollectionUtils.isEmpty(dataReplayFileOperateVo.getUpdateTime()) ? null : dataReplayFileOperateVo.getUpdateTime().get(0),
                    CollectionUtils.isEmpty(dataReplayFileOperateVo.getUpdateTime()) ? null : dataReplayFileOperateVo.getUpdateTime().get(1),
                    DATA_REPLAY_SUPPORT_FILE_CATEGORY,
                    dataReplayFileOperateVo.getTestProgramList());
        }

        preCheck(dataReplayFileOperateVo.getFileCategory(), CleanUpType.of(dataReplayFileOperateVo.getReplayType()),
                StepType.of(dataReplayFileOperateVo.getReplayStep()), fileWarehousingRecordIds);

        // 保存replay记录
        DataReplayRecord dataReplayRecord = DataReplayRecord.builder()
                .customer(RequestContext.getCustomer())
                .fileName(dataReplayFileOperateVo.getFileName())
                .remoteFilePath(dataReplayFileOperateVo.getRemoteFilePath())
                .testArea(TestArea.of(dataReplayFileOperateVo.getTestArea()))
                .factory(factoryListForSave)
                .fileCategory(dataReplayFileOperateVo.getFileCategory())
                .replayType(CleanUpType.of(dataReplayFileOperateVo.getReplayType()))
                .step(StepType.of(dataReplayFileOperateVo.getReplayStep()))
                .filterProcessStatusList(dataReplayFileOperateVo.getProcessStatusList())
                .filterExceptionMessage(dataReplayFileOperateVo.getExceptionMessage())
                .filterFileWarehousingRecordId(fileWarehousingRecordIdsFilterStr)
                .filterStepList(dataReplayFileOperateVo.getStepList())
                .startCreateTime(CollectionUtils.isEmpty(dataReplayFileOperateVo.getCreateTime()) ? null : dataReplayFileOperateVo.getCreateTime().get(0))
                .endCreateTime(CollectionUtils.isEmpty(dataReplayFileOperateVo.getCreateTime()) ? null : dataReplayFileOperateVo.getCreateTime().get(1))
                .startUpdateTime(CollectionUtils.isEmpty(dataReplayFileOperateVo.getUpdateTime()) ? null : dataReplayFileOperateVo.getUpdateTime().get(0))
                .endUpdateTime(CollectionUtils.isEmpty(dataReplayFileOperateVo.getUpdateTime()) ? null : dataReplayFileOperateVo.getUpdateTime().get(1))
                .testProgramList(dataReplayFileOperateVo.getTestProgramList())
                .fileWarehousingRecordId(fileWarehousingRecordIds.stream().map(String::valueOf).distinct().sorted().collect(Collectors.joining(Constant.COMMA)))
                .replayComments(dataReplayFileOperateVo.getReplayComments())
                .replayFileCnt(fileWarehousingRecordIds.size())
                .totalTaskCnt(0)
                .successTaskCnt(0)
                .failTaskCnt(0)
                .processStatus(ProcessStatus.CREATE)
                .createTime(new Date())
                .updateTime(new Date())
                .createUser(RequestContext.getUserName())
                .updateUser(RequestContext.getUserName())
                .build();
        this.saveDataReplayRecord(dataReplayRecord, fileWarehousingRecordIds);
        return null;
    }

    /**
     * 查询回放记录。
     *
     * @param dataReplayRecordQueryVo 查询条件对象，包含分页信息和过滤条件。
     * @return 返回回放记录的分页数据，包括总页数、当前页码、每页数据量和数据列表。
     */
    public PageableDataVo<DataReplayRecordVo> queryReplayRecord(DataReplayRecordQueryVo dataReplayRecordQueryVo) {
        log.info("queryReplayRecord");
        dataReplayRecordRepository.findAllByCustomerAndProcessStatusAndUpdateTimeBefore(RequestContext.getCustomer(), ProcessStatus.PROCESSING, new Date(System.currentTimeMillis() - 60 * 1000))
                .forEach(this::syncReplayRecordProgress);
        Pageable pageable = PageRequest.of(dataReplayRecordQueryVo.getJpaPageIndex(), dataReplayRecordQueryVo.getPageSize());

        // 处理Factory字段：如果包含(ALL)，则不过滤Factory
        List<String> factoryList = dataReplayRecordQueryVo.getFactoryList();
        if (factoryList != null && factoryList.contains("(ALL)")) {
            factoryList = null;
        }

        Page<DataReplayRecord> dataReplayRecordPage = dataReplayRecordRepository.findDataReplayRecord(
                RequestContext.getCustomer(),
                dataReplayRecordQueryVo.getTestArea(),
                JsonUtil.toJsonArrayString(factoryList),
                JsonUtil.toJsonArrayString(dataReplayRecordQueryVo.getDeviceIdList()),
                FileCategory.of(dataReplayRecordQueryVo.getFileCategory()),
                JsonUtil.toJsonArrayString(dataReplayRecordQueryVo.getLotIdList()),
                JsonUtil.toJsonArrayString(dataReplayRecordQueryVo.getWaferNoList()),
                JsonUtil.toJsonArrayString(dataReplayRecordQueryVo.getSblotIdList()),
                dataReplayRecordQueryVo.getLotType(),
                JsonUtil.toJsonArrayString(dataReplayRecordQueryVo.getTestStageList()),
                JsonUtil.toJsonArrayString(dataReplayRecordQueryVo.getTestProgramList()),
                dataReplayRecordQueryVo.getReplayTypeList() == null ? null : dataReplayRecordQueryVo.getReplayTypeList().stream().map(CleanUpType::getType).collect(Collectors.toList()),
                dataReplayRecordQueryVo.getStepList(),
                JsonUtil.toJsonArrayString(dataReplayRecordQueryVo.getFilterProcessStatusList()),
                dataReplayRecordQueryVo.getExceptionMessageList(),
                CollectionUtils.isEmpty(dataReplayRecordQueryVo.getFilterCreateTime()) ? null : dataReplayRecordQueryVo.getFilterCreateTime().get(0),
                CollectionUtils.isEmpty(dataReplayRecordQueryVo.getFilterCreateTime()) ? null : dataReplayRecordQueryVo.getFilterCreateTime().get(1),
                CollectionUtils.isEmpty(dataReplayRecordQueryVo.getFilterUpdateTime()) ? null : dataReplayRecordQueryVo.getFilterUpdateTime().get(0),
                CollectionUtils.isEmpty(dataReplayRecordQueryVo.getFilterUpdateTime()) ? null : dataReplayRecordQueryVo.getFilterUpdateTime().get(1),
                CollectionUtils.isEmpty(dataReplayRecordQueryVo.getCreateTime()) ? null : dataReplayRecordQueryVo.getCreateTime().get(0),
                CollectionUtils.isEmpty(dataReplayRecordQueryVo.getCreateTime()) ? null : dataReplayRecordQueryVo.getCreateTime().get(1),
                CollectionUtils.isEmpty(dataReplayRecordQueryVo.getUpdateTime()) ? null : dataReplayRecordQueryVo.getUpdateTime().get(0),
                CollectionUtils.isEmpty(dataReplayRecordQueryVo.getUpdateTime()) ? null : dataReplayRecordQueryVo.getUpdateTime().get(1),
                dataReplayRecordQueryVo.getProcessStatusList(),
                dataReplayRecordQueryVo.getFileName(),
                pageable
        );

        return PageableDataVo.of(dataReplayRecordPage.map(DataReplayRecordVo::of));
    }

    public Void retryReplay(Long id) {
        log.info("重试重播任务：replayRecordId = {}", id);
        Long processingDataReplayRecordCount  = dataReplayRecordRepository.countByCustomerAndProcessStatusIn(RequestContext.getCustomer(), Arrays.asList(ProcessStatus.PROCESSING));
        if (processingDataReplayRecordCount > 0) {
            throw new BpmsException(DATA_REPLAY_PROHIBIT_SUBMIT);
        }
        DataReplayRecord dataReplayRecord = dataReplayRecordRepository.findByIdAndCustomer(id, RequestContext.getCustomer()).orElseThrow(() -> new BpmsException(COMMON_OPERATE_NOT_PRESENT));
        if (dataReplayRecord.getFailTaskCnt() == 0) {
            throw new BpmsException(DATA_REPLAY_TASK_NOT_FAIL);
        }
        if (dataReplayRecord.getStep() == StepType.STEP_TYPE_2200) {
            log.info("重试重播2200任务，检测文件是否转换完成");
            boolean convertFinish = Arrays.stream(dataReplayRecord.getFileWarehousingRecordId().split(Constants.COMMA))
                    .mapToLong(Long::parseLong)
                    .anyMatch(fileWarehousingRecordId -> fileWarehousingRecordRepository.countConvertedFile(fileWarehousingRecordId) > 0);
            if (convertFinish) {
                throw new BpmsException(DATA_REPLAY_CONVERT_FINISHED_FILE);
            }
        }
        retryReplayTask(id);
        return null;
    }

    public Void cancelReplay(Long id) {
        log.info("取消重播任务：replayRecordId = {}", id);
        dataReplayRecordRepository.updateProcessStatusByIdAndProcessStatus(id, ProcessStatus.CREATE, ProcessStatus.CANCEL);
        return null;
    }

    public Void topReplay(Long id) {
        log.info("置顶重播任务：replayRecordId = {}", id);
        int updated = dataReplayRecordRepository.updateToppedTimeByIdAndProcessStatus(
                id,
                ProcessStatus.CREATE,
                RequestContext.getCustomer()
        );
        if (updated == 0) {
            if (!dataReplayRecordRepository.existsByIdAndCustomer(id, RequestContext.getCustomer())) {
                throw new BpmsException(COMMON_OPERATE_NOT_PRESENT);
            }
            throw new BpmsException(DATA_REPLAY_PROHIBIT_TOP);
        }
        return null;
    }

    public List<DataReplayTaskVo> queryReplayTask(DataReplayTaskQueryVo dataReplayTaskQueryVo) {
        log.info("查询重播任务, dataReplayRecordId: {}", dataReplayTaskQueryVo.getId());
        List<CleanupTask> cleanupTaskList = cleanupTaskRepository.findAllByReplayRecordIdAndFilters(dataReplayTaskQueryVo.getId(), RequestContext.getCustomer(),
                dataReplayTaskQueryVo.getLotIdList(), dataReplayTaskQueryVo.getWaferNoList(), dataReplayTaskQueryVo.getTestStageList(),
                dataReplayTaskQueryVo.getStatusList() == null ? null : dataReplayTaskQueryVo.getStatusList().stream().map(ProcessStatus::name).collect(Collectors.toList()),
                dataReplayTaskQueryVo.getExceptionMessageList());
        log.info("查询重播任务结果, dataReplayRecordId: {}, dataSize: {}", dataReplayTaskQueryVo.getId(), cleanupTaskList.size());
        return cleanupTaskList.stream()
                .map(DataReplayTaskVo::of)
                .collect(Collectors.toList());
    }

    public PageableDataVo<DataReplayTaskVo> queryReplayTaskPage(DataReplayTaskQueryPageVo dataReplayTaskQueryPageVo) {
        log.info("查询重播任务, dataReplayRecordId: {}", dataReplayTaskQueryPageVo.getId());
        Pageable pageable = PageRequest.of(dataReplayTaskQueryPageVo.getJpaPageIndex(), dataReplayTaskQueryPageVo.getPageSize());

        Page<CleanupTask> cleanupTaskPage = cleanupTaskRepository.findPageByReplayRecordIdAndFilters(dataReplayTaskQueryPageVo.getId(), RequestContext.getCustomer(),
                dataReplayTaskQueryPageVo.getLotIdList(), dataReplayTaskQueryPageVo.getWaferNoList(), dataReplayTaskQueryPageVo.getTestStageList(),
                dataReplayTaskQueryPageVo.getStatusList() == null ? null : dataReplayTaskQueryPageVo.getStatusList().stream().map(ProcessStatus::name).collect(Collectors.toList()),
                dataReplayTaskQueryPageVo.getExceptionMessageList(), pageable);
        log.info("查询重播任务结果, dataReplayRecordId: {}, dataSize: {}, 当前页: {}", dataReplayTaskQueryPageVo.getId(), cleanupTaskPage.getTotalElements(), dataReplayTaskQueryPageVo.getJpaPageIndex() + 1);

        return PageableDataVo.of(cleanupTaskPage.map(DataReplayTaskVo::of));
    }


    private void preCheck(FileCategory fileCategory, CleanUpType cleanUpType, StepType stepType, List<Long> fileWarehousingRecordIds) {
        Map<Pair<FileCategory, CleanUpOperation>, List<StepType>> supportedStepTypes = ImmutableMap.<Pair<FileCategory, CleanUpOperation>, List<StepType>>builder()
                // DELETE：STEP_TYPE_1000
                .put(Pair.of(FileCategory.STDF, CleanUpOperation.DELETE), ImmutableList.of(StepType.STEP_TYPE_1000))
                .put(Pair.of(FileCategory.RAW_DATA, CleanUpOperation.DELETE), ImmutableList.of(StepType.STEP_TYPE_1000))
                .put(Pair.of(FileCategory.BIT_MEM, CleanUpOperation.DELETE), ImmutableList.of(StepType.STEP_TYPE_1000))
                .put(Pair.of(FileCategory.WAT, CleanUpOperation.DELETE), ImmutableList.of(StepType.STEP_TYPE_1000))
                .put(Pair.of(FileCategory.SUMMARY, CleanUpOperation.DELETE), ImmutableList.of(StepType.STEP_TYPE_1000))
                // REPROCESS
                .put(Pair.of(FileCategory.STDF, CleanUpOperation.REPROCESS), ImmutableList.of(StepType.STEP_TYPE_3100, StepType.STEP_TYPE_4100, StepType.STEP_TYPE_5100))
                .put(Pair.of(FileCategory.RAW_DATA, CleanUpOperation.REPROCESS), ImmutableList.of(StepType.STEP_TYPE_2200, StepType.STEP_TYPE_3100, StepType.STEP_TYPE_4100, StepType.STEP_TYPE_5100))
                .put(Pair.of(FileCategory.BIT_MEM, CleanUpOperation.REPROCESS), ImmutableList.of(StepType.STEP_TYPE_2200, StepType.STEP_TYPE_3100, StepType.STEP_TYPE_4100, StepType.STEP_TYPE_5100))
                .put(Pair.of(FileCategory.WAT, CleanUpOperation.REPROCESS), ImmutableList.of(StepType.STEP_TYPE_2200))
                .put(Pair.of(FileCategory.SUMMARY, CleanUpOperation.REPROCESS), ImmutableList.of(StepType.STEP_TYPE_2200))
                .build();
        if (!supportedStepTypes.getOrDefault(Pair.of(fileCategory, cleanUpType.getOperation()), new ArrayList<>()).contains(stepType)) {
            log.info("不支持的重播类型和步骤， fileCategory = {}, cleanupType = {}, step = {}", fileCategory, cleanUpType, stepType);
            throw new BpmsException(DATA_REPLAY_UNSUPPORTED_TYPE_STEP);
        }

        if (CollectionUtils.isEmpty(fileWarehousingRecordIds)) {
            log.info("没有需要重播的文件");
            throw new BpmsException(ResponseCode.Business.DATA_REPLAY_FILE_NOT_EXISTS);
        }

        if (fileWarehousingRecordIds.size() > replayFileMaxCount) {
            log.info("文件数量超过最大限制，文件数量：" + fileWarehousingRecordIds.size() + ",最大限制：" + replayFileMaxCount);
            throw new BpmsException(ResponseCode.Business.DATA_REPLAY_TOO_MANY_FILE);
        }

        if (fileWarehousingRecordRepository.countByIdInAndProcessStatusAndStepLessThan(fileWarehousingRecordIds, ProcessStatus.FAIL, stepType.getStep()) != 0) {
            log.info("禁止提交任务,存在失败文件当前Step大于重播步骤, replayStep = {}", stepType.getStep());
            throw new BpmsException(ResponseCode.Business.DATA_REPLAY_STEP_NOT_SUPPORT);
        }

        if (Objects.equals(fileCategory, FileCategory.SUMMARY) && Objects.equals(CleanUpOperation.DELETE, cleanUpType.getOperation()) && fileWarehousingRecordIds.size() > 1) {
            log.info("禁止提交任务,删除任务存在多个summary文件");
            throw new BpmsException(ResponseCode.Business.DATA_REPLAY_DELETE_SUMMARY_TOO_MANY);
        }

    }

    /**
     * 生成重播任务
     *
     * @param dataReplayRecord dataReplayRecord
     */
    public void createReplayTask(DataReplayRecord dataReplayRecord) {
        try {
            List<Long> fileWarehousingRecordIds = Stream.of(dataReplayRecord.getFileWarehousingRecordId().split(Constant.COMMA)).map(Long::parseLong).collect(Collectors.toList());
            log.info("生成replay任务, fileWarehousingRecordIds = {}", fileWarehousingRecordIds);
            ReplayVo replayVo = new ReplayVo()
                    .setReplayRecordId(dataReplayRecord.getId())
                    .setStepType(dataReplayRecord.getStep())
                    .setCleanUpType(dataReplayRecord.getReplayType())
                    .setFileWarehousingRecordIds(fileWarehousingRecordIds);
            // 生成replay任务
            ReplayMessage replayMessage = dataRepairRpcService.createTask(replayVo);

            // 更新replay记录任务信息
            dataReplayRecord.setTotalTaskCnt(replayMessage.getTotalTaskCnt());
            dataReplayRecord.setSuccessTaskCnt(replayMessage.getSuccessTaskCnt());
            dataReplayRecord.setFailTaskCnt(replayMessage.getFailTaskCnt());
            dataReplayRecord.setProcessStatus(replayMessage.getProcessStatus());
            dataReplayRecord.setUpdateTime(new Date());
            dataReplayRecordRepository.save(dataReplayRecord);
            log.info("更新replay记录任务信息， dataReplayRecordId = {}, status = {}", dataReplayRecord.getId(), dataReplayRecord.getProcessStatus());
        } catch (Exception ex) {
            log.error("生成replay任务失败, dataReplayRecordId = {}", dataReplayRecord.getId());
            dataReplayRecord.setProcessStatus(ProcessStatus.FAIL);
            dataReplayRecord.setUpdateTime(new Date());
            dataReplayRecordRepository.save(dataReplayRecord);
            throw ex;
        }
    }

    /**
     * 消费数据处理结束消息，更新重播任务和重播记录
     *
     * @param taskIds       taskIds
     * @param processStatus processStatus
     */
    public void updateReplayTask(List<Long> taskIds, ProcessStatus processStatus, ExceptionType exceptionType, String exceptionMessage, String errorMessage) {
        log.info("更新重播任务状态：taskIds = {}， processStatus = {}, exceptionMessage: {}", taskIds, processStatus, exceptionMessage);
        ReplayTaskVo replayTaskVo = new ReplayTaskVo()
                .setCleanupTaskIds(taskIds)
                .setProcessStatus(processStatus)
                .setExceptionType(exceptionType)
                .setExceptionMessage(exceptionMessage)
                .setErrorMessage(errorMessage);
        List<ReplayMessage> replayMessageList = dataRepairRpcService.updateTask(replayTaskVo);
        if (CollectionUtils.isEmpty(replayMessageList)) {
            log.info("更新重播任务状态失败，taskIds = {}， processStatus = {}", taskIds, processStatus);
        } else {
            replayMessageList.forEach(replayMessage ->
                    dataReplayRecordRepository.findById(replayMessage.getReplayRecordId()).ifPresent(dataReplayRecord -> {
                        // 更新replay记录任务信息
                        log.info("更新重播记录状态：replayRecordId = {}， processStatus = {}", replayMessage.getReplayRecordId(), replayMessage.getProcessStatus());
                        dataReplayRecord.setTotalTaskCnt(replayMessage.getTotalTaskCnt());
                        dataReplayRecord.setSuccessTaskCnt(replayMessage.getSuccessTaskCnt());
                        dataReplayRecord.setFailTaskCnt(replayMessage.getFailTaskCnt());
                        dataReplayRecord.setProcessStatus(replayMessage.getProcessStatus());
                        dataReplayRecord.setUpdateTime(new Date());
                        dataReplayRecordRepository.save(dataReplayRecord);
                    }));
        }

    }

    private void retryReplayTask(Long replayRecordId) {
        try {
            log.info("重试任务：replayRecordId = {}", replayRecordId);
            ReplayMessage replayMessage = dataRepairRpcService.retryTask(replayRecordId);
            dataReplayRecordRepository.findById(replayMessage.getReplayRecordId()).ifPresent(dataReplayRecord -> {
                // 更新replay记录任务信息
                log.info("更新重播记录状态：replayRecordId = {}， processStatus = {}", replayMessage.getReplayRecordId(), replayMessage.getProcessStatus());
                dataReplayRecord.setTotalTaskCnt(replayMessage.getTotalTaskCnt());
                dataReplayRecord.setSuccessTaskCnt(replayMessage.getSuccessTaskCnt());
                dataReplayRecord.setFailTaskCnt(replayMessage.getFailTaskCnt());
                dataReplayRecord.setProcessStatus(replayMessage.getProcessStatus());
                dataReplayRecord.setUpdateTime(new Date());
                dataReplayRecordRepository.save(dataReplayRecord);
            });
        } catch (Exception ex) {
            log.info("任务重播失败：replayRecordId = {}", replayRecordId);
            dataReplayRecordRepository.findById(replayRecordId).ifPresent(dataReplayRecord -> {
                // 更新replay记录任务信息
                log.info("更新重播记录状态：replayRecordId = {}， processStatus = {}", replayRecordId, ProcessStatus.FAIL);
                dataReplayRecord.setTotalTaskCnt(0);
                dataReplayRecord.setSuccessTaskCnt(0);
                dataReplayRecord.setFailTaskCnt(0);
                dataReplayRecord.setProcessStatus(ProcessStatus.FAIL);
                dataReplayRecord.setUpdateTime(new Date());
                dataReplayRecordRepository.save(dataReplayRecord);
            });
        }

    }

    boolean hasWaferInfo(CleanupTask cleanupTask) {
        boolean hasLotInfoFlag = StringUtils.isNotBlank(cleanupTask.getCustomer()) && Objects.nonNull(cleanupTask.getTestArea()) && StringUtils.isNotBlank(cleanupTask.getFactory()) && StringUtils.isNotBlank(cleanupTask.getDeviceId())
                && Objects.nonNull(cleanupTask.getLotType()) && StringUtils.isNotBlank(cleanupTask.getTestStage()) && StringUtils.isNotBlank(cleanupTask.getLotId());
        return TestArea.getCPList().contains(cleanupTask.getTestArea()) ? hasLotInfoFlag && StringUtils.isNotBlank(cleanupTask.getWaferNo()) : hasLotInfoFlag;
    }

    public void syncReplayRecordProgress(DataReplayRecord dataReplayRecord) {
        log.info("刷新重播记录状态，replayRecordId: {}", dataReplayRecord.getId());
        List<CleanupTask> cleanupTaskList = cleanupTaskRepository.findAllByDataReplayRecordId(dataReplayRecord.getId());
        this.checkAndUpdatePendingTask(cleanupTaskList);
        if (cleanupTaskList.stream().allMatch(elem -> Arrays.asList(ProcessStatus.SUCCESS, ProcessStatus.FAIL).contains(elem.getProcessStatus()))) {
            Map<ProcessStatus, Long> statusCntMap = cleanupTaskList.stream().collect(Collectors.groupingBy(CleanupTask::getProcessStatus, Collectors.counting()));
            dataReplayRecord.setTotalTaskCnt(cleanupTaskList.size());
            dataReplayRecord.setSuccessTaskCnt(statusCntMap.getOrDefault(ProcessStatus.SUCCESS, 0L).intValue());
            dataReplayRecord.setFailTaskCnt(statusCntMap.getOrDefault(ProcessStatus.FAIL, 0L).intValue());
            if (dataReplayRecord.getTotalTaskCnt() == dataReplayRecord.getSuccessTaskCnt() + dataReplayRecord.getFailTaskCnt()) {
                dataReplayRecord.setProcessStatus(Objects.equals(dataReplayRecord.getSuccessTaskCnt(), dataReplayRecord.getTotalTaskCnt()) ? ProcessStatus.SUCCESS : ProcessStatus.FAIL);
            } else {
                if (cleanupTaskList.stream().allMatch(elem -> ProcessStatus.SUCCESS == elem.getProcessStatus())) {
                    dataReplayRecord.setProcessStatus(ProcessStatus.SUCCESS);
                } else if (cleanupTaskList.stream().allMatch(elem -> ProcessStatus.FAIL == elem.getProcessStatus())) {
                    dataReplayRecord.setProcessStatus(ProcessStatus.FAIL);
                }
            }
            log.info("任务处理完成, 更新重播记录状态 status = {}", dataReplayRecord.getProcessStatus());
            dataReplayRecordRepository.updateProcessStatusById(dataReplayRecord.getId(), dataReplayRecord.getProcessStatus(), dataReplayRecord.getTotalTaskCnt(), dataReplayRecord.getSuccessTaskCnt(), dataReplayRecord.getFailTaskCnt());
        }
    }

    private String wrapperBlankString(String str) {
        return str == null ? Constant.WRAPPER_NULL : str;
    }


    private void saveDataReplayRecord(DataReplayRecord dataReplayRecord, List<Long> fileWarehousingRecordIds) {
        if (fileWarehousingRecordIds.size() >= replayFileBatchSize) {
            log.info("文件数量超出限制, {} >= {}, 分批处理", fileWarehousingRecordIds.size(), replayFileBatchSize);

            List<FileWarehousingMinVo> waferFileNameVoList = fileWarehousingRecordRepository.findFileWarehousingMinVoByIdIn(fileWarehousingRecordIds);

            for (List<FileWarehousingMinVo> fileWarehousingMinVoList : ListUtils.partition(waferFileNameVoList, replayFileBatchSize)) {
                DataReplayRecord targetDataReplayRecord = new DataReplayRecord();
                BeanUtils.copyProperties(dataReplayRecord, targetDataReplayRecord);

                List<Long> fileWarehousingMinVoIds = fileWarehousingMinVoList.stream().map(FileWarehousingMinVo::getId).collect(Collectors.toList());

                targetDataReplayRecord.setFileWarehousingRecordId(fileWarehousingMinVoIds.stream().map(String::valueOf).distinct().sorted().collect(Collectors.joining(Constant.COMMA)));
                targetDataReplayRecord.setReplayFileCnt(fileWarehousingMinVoIds.size());
                dataReplayRecordRepository.save(targetDataReplayRecord);
                log.info("保存replay记录成功， dataReplayRecordId = {}", targetDataReplayRecord.getId());
            }
        } else {
            dataReplayRecordRepository.save(dataReplayRecord);
            log.info("保存replay记录成功， dataReplayRecordId = {}", dataReplayRecord.getId());
        }
    }

    /**
     * 检查重播任务进度，重播任务防呆
     */
    private void checkAndUpdatePendingTask(List<CleanupTask> cleanupTaskList) {
        if (CollectionUtils.isEmpty(cleanupTaskList)) {
            return;
        }
        List<CleanupTask> processingCleanupTaskList = cleanupTaskList.stream()
                // 筛选出待处理的重播重跑任务
                .filter(elem -> elem.getCleanupType().getOperation() == CleanUpOperation.REPROCESS)
                .filter(elem -> elem.getProcessStatus() == ProcessStatus.PROCESSING)
                .filter(elem -> elem.getCreateTime().toInstant().isBefore(Instant.now().minus(6, ChronoUnit.HOURS)))
                .collect(Collectors.toList());
        for (CleanupTask cleanupTask : processingCleanupTaskList) {
            log.info("检查重播任务是否超时, cleanupTaskIds:{}", cleanupTaskList.stream().map(CleanupTask::getId).collect(Collectors.toList()));

            List<String> fileList = new ArrayList<>(cleanupTask.getFileList());
            List<FileWarehousingRecord> fileWarehousingRecordList = fileWarehousingRecordRepository.findAllByFileNameIn(fileList);
            if (fileWarehousingRecordList.stream().anyMatch(elem -> elem.getProcessStatus() == ProcessStatus.FAIL)) {
                // 存在失败文件
                log.info("存在失败文件");
                cleanupTask.setProcessStatus(ProcessStatus.FAIL)
                        .setExceptionType(fileWarehousingRecordList.get(0).getExceptionType())
                        .setExceptionMessage(fileWarehousingRecordList.get(0).getExceptionMessage())
                        .setErrorMessage(fileWarehousingRecordList.get(0).getErrorMessage())
                        .setUpdateTime(new Date());
                cleanupTaskRepository.save(cleanupTask);
            } else if (fileWarehousingRecordList.stream().allMatch(elem -> elem.getProcessStatus() == ProcessStatus.SUCCESS)) {
                // 所有文件处理成功
                log.info("所有文件处理成功");
                cleanupTask.setProcessStatus(ProcessStatus.SUCCESS).setUpdateTime(new Date());
                cleanupTaskRepository.save(cleanupTask);
            } else {
                List<FileWarehousingRecord> processingFileWarehousingRecord = fileWarehousingRecordList.stream().filter(elem -> elem.getProcessStatus() == ProcessStatus.PROCESSING).collect(Collectors.toList());

                int i = 0;
                ProcessStatus processStatus = null;
                while (i < fileWarehousingRecordList.size() && !Objects.equals(processStatus, ProcessStatus.CREATE)) {
                    FileWarehousingRecord fileWarehousingRecord = processingFileWarehousingRecord.get(i);
                    StepType step = StepType.of(fileWarehousingRecord.getStep());

                    if (step == StepType.STEP_TYPE_1000 || step == StepType.STEP_TYPE_2200) {
                        Optional<SftpFileDetail> sftpFileDetail = sftpFileDetailRepository.findByLocalFileName(fileWarehousingRecord.getFileName());
                        if (sftpFileDetail.isPresent()) {
                            processStatus = sftpFileDetail.get().getProcessStatus();
                        }
                    } else if (step == StepType.STEP_TYPE_3100) {
                        List<LotMetaDataDetail> lotMetaDataDetailList = lotMetaDataDetailRepository.findAllByFileNameIn(Collections.singletonList(fileWarehousingRecord.getFileName()));
                        if (CollectionUtils.isNotEmpty(lotMetaDataDetailList)) {
                            processStatus = lotMetaDataDetailList.get(0).getProcessStatus();
                        }
                    } else if (step == StepType.STEP_TYPE_4100 ) {
                        List<LayerCalculatePool> layerCalculatePoolList = layerCalculatePoolRepository.findAllByCustomerAndSubCustomerAndFactoryAndTestAreaAndDeviceIdAndTestStageAndLotIdAndWaferNoAndLotTypeAndFileCategoryAndDwLayerAndProcessStatusOrderByUpdateTimeDesc(
                                fileWarehousingRecord.getCustomer(),
                                fileWarehousingRecord.getSubCustomer(),
                                fileWarehousingRecord.getFactory(),
                                fileWarehousingRecord.getTestArea(),
                                fileWarehousingRecord.getDeviceId(),
                                fileWarehousingRecord.getTestStage(),
                                fileWarehousingRecord.getLotId(),
                                fileWarehousingRecord.getWaferNo(),
                                fileWarehousingRecord.getLotType(),
                                fileWarehousingRecord.getFileCategory(),
                                DwLayer.DWD,
                                ProcessStatus.CREATE
                        );
                        if (CollectionUtils.isNotEmpty(layerCalculatePoolList)) {
                            processStatus = ProcessStatus.CREATE;
                        }
                    } else if (step == StepType.STEP_TYPE_5100|| step == StepType.STEP_TYPE_7100) {
                        List<BzAppInstance> appInstanceList = bzAppInstanceRepository.findAllByCustomerAndSubCustomerAndFactoryAndTestAreaAndDeviceIdAndTestStageAndLotIdAndWaferNoAndLotTypeAndOptTypeAndProcessStatus(
                                fileWarehousingRecord.getCustomer(),
                                fileWarehousingRecord.getSubCustomer(),
                                fileWarehousingRecord.getFactory(),
                                fileWarehousingRecord.getTestArea(),
                                fileWarehousingRecord.getDeviceId(),
                                fileWarehousingRecord.getTestStage(),
                                fileWarehousingRecord.getLotId(),
                                fileWarehousingRecord.getWaferNo(),
                                fileWarehousingRecord.getLotType(),
                                OptType.NEW_TEST_ITEM_DATA,
                                ProcessStatus.CREATE
                        );
                        if (CollectionUtils.isNotEmpty(appInstanceList)) {
                            processStatus = ProcessStatus.CREATE;
                        }
                    }
                    if (Objects.equals(processStatus, ProcessStatus.CREATE)) {
                        log.info("文件处理中, cleanupTaskId = {}", cleanupTask.getId());
                    } else {
                        log.info("重播任务超时, cleanupTaskId = {}", cleanupTask.getId());
                        cleanupTask.setProcessStatus(ProcessStatus.FAIL)
                                .setExceptionType(ExceptionType.OTHER_EXCEPTION)
                                .setExceptionMessage("重播任务超时")
                                .setErrorMessage("重播任务超时")
                                .setUpdateTime(new Date());
                        cleanupTaskRepository.save(cleanupTask);
                    }
                }
            }
        }
    }

    /**
     * 获取数据入流程详情
     */
    public DataReplayFileProcessDetailVo getFileProcessDetail(Long id) {
        FileWarehousingRecord fileWarehousingRecord = fileWarehousingRecordRepository.findById(id).orElseThrow(() -> new BpmsException(ResponseCode.Common.COMMON_OPERATE_NOT_PRESENT));

        DataReplayFileProcessDetailVo dataReplayFileProcessDetailVo = DataReplayFileProcessDetailVo.builder()
                .fileName(fileWarehousingRecord.getFileName())
                .testArea(TestArea.of(fileWarehousingRecord.getTestArea()))
                .factory(fileWarehousingRecord.getFactory())
                .deviceId(fileWarehousingRecord.getDeviceId())
                .fileCategory(FileCategory.of(fileWarehousingRecord.getFileCategory()))
                .lotId(fileWarehousingRecord.getLotId())
                .waferNo(fileWarehousingRecord.getWaferNo())
                .lotType(LotType.of(fileWarehousingRecord.getLotType()))
                .testStage(fileWarehousingRecord.getTestStage())
                .processStatus(fileWarehousingRecord.getProcessStatus())
                .build();
        List<FileProcessStepVo> fileProcessStepVoList = fileLoadingLogRepository.findFileProcessStepVoByStepLessThanEqual(fileWarehousingRecord.getFileName(), fileWarehousingRecord.getStep());
        if (fileWarehousingRecord.getProcessStatus() == ProcessStatus.PROCESSING) {
            StepType step = StepType.of(fileWarehousingRecord.getStep());
            boolean isPending = false;
            StepType currentStep = null;
            Date updateTime = null;
            List<FileCategory> singleFileCategory = Arrays.asList(FileCategory.WAT, FileCategory.SUMMARY);
            if (fileWarehousingRecord.getStep() == StepType.STEP_TYPE_1000.getStep()) {
                Optional<SftpFileDetail> sftpFileDetail = sftpFileDetailRepository.findByLocalFileName(fileWarehousingRecord.getFileName());
                if (sftpFileDetail.isPresent() && sftpFileDetail.get().getProcessStatus() == ProcessStatus.CREATE) {
                    isPending = true;
                    currentStep = StepType.STEP_TYPE_2200;
                    if (Objects.equals(0, fileWarehousingRecord.getConvertFlag())) {
                        currentStep = singleFileCategory.contains(fileWarehousingRecord.getFileCategory()) ? StepType.STEP_TYPE_7100 : StepType.STEP_TYPE_3100;
                    }
                    updateTime = sftpFileDetail.get().getUpdateTime();
                }
            } else if (step == StepType.STEP_TYPE_3100) {
                List<LotMetaDataDetail> lotMetaDataDetailList = lotMetaDataDetailRepository.findAllByFileNameIn(Collections.singletonList(fileWarehousingRecord.getFileName()));
                if (CollectionUtils.isNotEmpty(lotMetaDataDetailList) && lotMetaDataDetailList.get(0).getProcessStatus() == ProcessStatus.CREATE) {
                    isPending = true;
                    currentStep = StepType.STEP_TYPE_4100;
                    updateTime = lotMetaDataDetailList.get(0).getUpdateTime();
                }
            } else if (step == StepType.STEP_TYPE_4100 ) {
                List<LayerCalculatePool> layerCalculatePoolList = layerCalculatePoolRepository.findAllByCustomerAndSubCustomerAndFactoryAndTestAreaAndDeviceIdAndTestStageAndLotIdAndWaferNoAndLotTypeAndFileCategoryAndDwLayerAndProcessStatusOrderByUpdateTimeDesc(
                        fileWarehousingRecord.getCustomer(),
                        fileWarehousingRecord.getSubCustomer(),
                        fileWarehousingRecord.getFactory(),
                        fileWarehousingRecord.getTestArea(),
                        fileWarehousingRecord.getDeviceId(),
                        fileWarehousingRecord.getTestStage(),
                        fileWarehousingRecord.getLotId(),
                        fileWarehousingRecord.getWaferNo(),
                        fileWarehousingRecord.getLotType(),
                        fileWarehousingRecord.getFileCategory(),
                        DwLayer.ODS,
                        ProcessStatus.CREATE
                );
                if (CollectionUtils.isEmpty(layerCalculatePoolList)) {
                    layerCalculatePoolList = layerCalculatePoolRepository.findAllByCustomerAndSubCustomerAndFactoryAndTestAreaAndDeviceIdAndTestStageAndLotIdAndWaferNoAndLotTypeAndFileCategoryAndDwLayerAndProcessStatusOrderByUpdateTimeDesc(
                            fileWarehousingRecord.getCustomer(),
                            fileWarehousingRecord.getSubCustomer(),
                            fileWarehousingRecord.getFactory(),
                            fileWarehousingRecord.getTestArea(),
                            fileWarehousingRecord.getDeviceId(),
                            fileWarehousingRecord.getTestStage(),
                            fileWarehousingRecord.getLotId(),
                            fileWarehousingRecord.getWaferNo(),
                            fileWarehousingRecord.getLotType(),
                            fileWarehousingRecord.getFileCategory(),
                            DwLayer.DWD,
                            ProcessStatus.CREATE
                    );
                }
                if (CollectionUtils.isNotEmpty(layerCalculatePoolList)) {
                    isPending = true;
                    currentStep = StepType.STEP_TYPE_5100;
                    updateTime = layerCalculatePoolList.get(0).getUpdateTime();
                }
            } else if ((step == StepType.STEP_TYPE_5100|| step == StepType.STEP_TYPE_7100) && !singleFileCategory.contains(fileWarehousingRecord.getFileCategory())) {
                List<BzAppInstance> appInstanceList = bzAppInstanceRepository.findAllByCustomerAndSubCustomerAndFactoryAndTestAreaAndDeviceIdAndTestStageAndLotIdAndWaferNoAndLotTypeAndOptTypeAndProcessStatus(
                        fileWarehousingRecord.getCustomer(),
                        fileWarehousingRecord.getSubCustomer(),
                        fileWarehousingRecord.getFactory(),
                        fileWarehousingRecord.getTestArea(),
                        fileWarehousingRecord.getDeviceId(),
                        fileWarehousingRecord.getTestStage(),
                        fileWarehousingRecord.getLotId(),
                        fileWarehousingRecord.getWaferNo(),
                        fileWarehousingRecord.getLotType(),
                        OptType.NEW_TEST_ITEM_DATA,
                        ProcessStatus.CREATE
                );
                if (CollectionUtils.isNotEmpty(appInstanceList)) {
                    isPending = true;
                    currentStep = StepType.STEP_TYPE_7100;
                    updateTime = appInstanceList.get(0).getUpdateTime();
                }
            }
            if (isPending) {
                FileProcessStepVo pendingFileProcessStepVo = FileProcessStepVo.builder()
                        .step(currentStep.getStep())
                        .updateTime(updateTime)
                        .processStatus(ProcessStatus.CREATE)
                        .build();
                if (currentStep.getStep() == StepType.STEP_TYPE_7100.getStep()) {
                    fileProcessStepVoList = fileProcessStepVoList.stream().filter(v -> v.getStep() != StepType.STEP_TYPE_7100.getStep()).collect(Collectors.toList());
                }
                fileProcessStepVoList.add(pendingFileProcessStepVo);
            }
        }
        List<FileProcessStep> fileProcessStepList = fileProcessStepVoList.stream().map(FileProcessStep::of).collect(Collectors.toList());
        dataReplayFileProcessDetailVo.setFileProcessStepList(fileProcessStepList);
        return dataReplayFileProcessDetailVo;
    }

    public List<DataReplayDropDownVo> getFactoryDropDown() {
        log.info("getFactoryDropDown");
        List<String> fieldResult = fileWarehousingRecordRepository.findDistinctFactory(RequestContext.getCustomer());
        fieldResult = fieldResult.stream()
                .map(this::wrapperBlankString)
                .distinct()
                .collect(Collectors.toList());

        fieldResult.add(0, "(ALL)");
        log.info("Factory下拉框结果 value:{}", String.join(Constant.COMMA, fieldResult));
        return DataReplayDropDownVo.ofList(fieldResult);
    }

    public DataReplayFileValidationVo validateFileNameList(DataReplayFileNameListVo dataReplayFileNameListVo) {
        log.info("验证文件名列表的Test Area和File Category, fileNames: {}", dataReplayFileNameListVo.getFileNameList());

        List<String> fileNameList = dataReplayFileNameListVo.getFileNameList();
        if (CollectionUtils.isEmpty(fileNameList)) {
            return DataReplayFileValidationVo.failure("文件名列表不能为空", Collections.emptyList());
        }

        try {
            // 查询文件名对应的Test Area和File Category
            List<Object[]> results = fileWarehousingRecordRepository.findTestAreaAndFileCategoryByFileNames(
                    RequestContext.getCustomer(), fileNameList);

            if (CollectionUtils.isEmpty(results)) {
                return DataReplayFileValidationVo.failure("未找到匹配的文件记录", Collections.emptyList());
            }

            // 将查询结果转换为组合对象列表
            List<DataReplayFileValidationVo.TestAreaFileCategoryCombination> combinations = results.stream()
                    .map(row -> new DataReplayFileValidationVo.TestAreaFileCategoryCombination(
                            (String) row[0], // test_area
                            (String) row[1], // file_category
                            ((Number) row[2]).longValue() // file_count
                    ))
                    .collect(Collectors.toList());

            if (combinations.size() == 1) {
                // 只有一种组合，验证成功
                DataReplayFileValidationVo.TestAreaFileCategoryCombination combination = combinations.get(0);
                log.info("文件名列表验证成功, Test Area: {}, File Category: {}",
                        combination.getTestArea(), combination.getFileCategory());
                return DataReplayFileValidationVo.success(combination.getTestArea(), combination.getFileCategory());
            } else {
                // 存在多种组合，验证失败
                String errorMessage = String.format("存在不同的Test Area或File Category，共发现%d种组合", combinations.size());
                log.warn("文件名列表验证失败: {}, 组合详情: {}", errorMessage, combinations);
                return DataReplayFileValidationVo.failure(errorMessage, combinations);
            }
        } catch (Exception e) {
            log.error("验证文件名列表时发生异常", e);
            return DataReplayFileValidationVo.failure("验证过程中发生错误: " + e.getMessage(), Collections.emptyList());
        }
    }
}
